import requests
import pdfkit

# 目标 URL
url = 'https://api-b.dcarapi.com/motor/feoffline/usedcar_detect_midas/midas-preview?app_id=motor.sh_inspection.pack_lib&inspection_id=R7493452322866122814&nonce_str=jpByQIjAnlxNOIwB&report_type=2&time_stamp=1745228869&sign=c7e3ad4458e08270884b2293ac11cb4b&page_id=11&share_did=2e4058ce2467b3ae430328aece2637bb&share_uid=8a6949406e03cc318d5e3f4ea881c25e&share_token=96aaabac-3cba-4f14-8bc6-7c6e5e9b6d7b'

# 设置请求头，模拟浏览器访问
headers = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36'
}
try:
    # 发送请求获取网页内容，添加超时设置
    response = requests.get(url, headers=headers, timeout=10)
    response.raise_for_status()  # 检查请求是否成功

    # 打印请求内容进行调试
    print("请求返回的内容：")
    print(response.text)

    html_content = response.text

    # 保存 HTML 文件进行调试
    html_file_path = '/Users/<USER>/aicode/debug.html'
    with open(html_file_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

    # 配置 pdfkit，使用找到的 wkhtmltopdf 路径
    config = pdfkit.configuration(wkhtmltopdf='/usr/local/bin/wkhtmltopdf')

    # 添加忽略网络错误的选项
    options = {
        'ignore-load-errors': '',
        'load-error-handling': 'ignore'
    }

    # 生成 PDF 文件
    pdfkit.from_file(html_file_path, 'vehicle_inspection_report.pdf', configuration=config, options=options)
    print('车辆检测报告已成功保存为 vehicle_inspection_report.pdf')
except requests.RequestException as req_err:
    print(f'请求发生错误: {req_err}')
except Exception as err:
    print(f'发生未知错误: {err}')
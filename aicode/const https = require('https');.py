const https = require('https');
const fs = require('fs');

// URL to fetch
const url = 'https://www.runoob.com/linux/linux-vim.html';

// Options for the HTTP request
const options = {
  headers: {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Cache-Control': 'max-age=0'
  }
};

console.log(`Fetching content from ${url}...`);

// Make the HTTP request
https.get(url, options, (res) => {
  let data = '';
  const contentType = res.headers['content-type'];
  
  console.log(`Status Code: ${res.statusCode}`);
  console.log(`Content-Type: ${contentType}`);
  
  // Check if the response is compressed
  const encoding = res.headers['content-encoding'];
  if (encoding) {
    console.log(`Content-Encoding: ${encoding}`);
  }

  // Handle the response data
  res.on('data', (chunk) => {
    data += chunk;
  });

  // Process the complete response
  res.on('end', () => {
    console.log(`Received ${data.length} bytes of data`);
    
    try {
      // Simple HTML parsing to extract text content
      // Remove HTML tags and keep only text
      let textContent = data
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
        .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')   // Remove styles
        .replace(/<head\b[^<]*(?:(?!<\/head>)<[^<]*)*<\/head>/gi, '')     // Remove head
        .replace(/<nav\b[^<]*(?:(?!<\/nav>)<[^<]*)*<\/nav>/gi, '')       // Remove navigation
        .replace(/<footer\b[^<]*(?:(?!<\/footer>)<[^<]*)*<\/footer>/gi, '') // Remove footer
        .replace(/<aside\b[^<]*(?:(?!<\/aside>)<[^<]*)*<\/aside>/gi, '')  // Remove sidebars
        .replace(/<header\b[^<]*(?:(?!<\/header>)<[^<]*)*<\/header>/gi, '') // Remove header
        .replace(/<[^>]+>/g, ' ')  // Remove remaining HTML tags
        .replace(/\s+/g, ' ')      // Replace multiple spaces with a single space
        .trim();                   // Trim leading/trailing spaces
      
      // Try to find the main content section
      // Look for common content markers in the HTML
      const mainContentRegex = /<div\s+class="content[^>]*>([\s\S]*?)<\/div>/i;
      const mainContentMatch = data.match(mainContentRegex);
      
      if (mainContentMatch && mainContentMatch[1]) {
        console.log('Found main content section. Extracting...');
        
        // Extract text from the main content section
        let mainContent = mainContentMatch[1]
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
          .replace(/<[^>]+>/g, '\n')  // Replace tags with newlines to preserve structure
          .replace(/\n+/g, '\n')      // Replace multiple newlines with a single newline
          .replace(/&nbsp;/g, ' ')    // Replace &nbsp; with spaces
          .replace(/&lt;/g, '<')      // Replace &lt; with <
          .replace(/&gt;/g, '>')      // Replace &gt; with >
          .replace(/&amp;/g, '&')     // Replace &amp; with &
          .replace(/&quot;/g, '"')    // Replace &quot; with "
          .trim();                    // Trim leading/trailing spaces
        
        // Save the main content to a file
        fs.writeFileSync('linux-vim-tutorial.txt', mainContent, 'utf8');
        console.log('Main content saved to linux-vim-tutorial.txt');
      } else {
        console.log('Could not find main content section. Saving full text content...');
        
        // Save the full text content to a file
        fs.writeFileSync('linux-vim-tutorial.txt', textContent, 'utf8');
        console.log('Full text content saved to linux-vim-tutorial.txt');
      }
    } catch (error) {
      console.error('Error processing the HTML content:', error);
    }
  });
}).on('error', (error) => {
  console.error(`Error fetching the webpage: ${error.message}`);
});

# CSV 转 Badcase JSON 格式转换工具

这个工具用于将CSV文件转换为特定的Badcase JSON格式，适用于保险大模型的训练数据。

## 功能特点

- 将CSV文件转换为特定的Badcase JSON格式
- 支持自定义CSV分隔符和引号字符
- 可控制JSON输出格式（缩进、键排序）
- 支持输出到文件或标准输出

## 使用方法

基本用法：

```bash
python csv_to_badcase_json.py 2025保险大模型-badcase-王斌.csv
```

这将把CSV文件转换为特定的JSON格式并打印到控制台。

### 保存到文件

```bash
python csv_to_badcase_json.py 2025保险大模型-badcase-王斌.csv -o badcase_output.json
```

### 自定义分隔符

如果你的CSV文件使用制表符作为分隔符：

```bash
python csv_to_badcase_json.py 2025保险大模型-badcase-王斌.csv -d $'\t'
```

### 自定义JSON格式

```bash
python csv_to_badcase_json.py 2025保险大模型-badcase-王斌.csv --indent 2 --sort-keys
```

## 完整参数列表

```
usage: csv_to_badcase_json.py [-h] [-o OUTPUT] [-d DELIMITER] [-q QUOTECHAR]
                              [--indent INDENT] [--sort-keys]
                              input_file

将 CSV 文件转换为特定的 badcase JSON 格式。

positional arguments:
  input_file            输入 CSV 文件的路径

optional arguments:
  -h, --help            show this help message and exit
  -o OUTPUT, --output OUTPUT
                        输出 JSON 文件的路径（默认：标准输出）
  -d DELIMITER, --delimiter DELIMITER
                        CSV 分隔符字符（默认：,）
  -q QUOTECHAR, --quotechar QUOTECHAR
                        CSV 引号字符（默认："）
  --indent INDENT       JSON 文件中缩进的空格数（默认：4）
  --sort-keys           在 JSON 输出中按字母顺序排序键
```

## 输出格式

转换后的JSON格式如下：

```json
[
    {
        "conversations": [
            {
                "from": "human",
                "value": "CSV中的value字段内容"
            }
        ],
        "chosen": {
            "from": "gpt",
            "value": "CSV中的chosen字段内容"
        },
        "rejected": {
            "from": "gpt",
            "value": "CSV中的rejected字段内容"
        }
    },
    ...
]
```

## 注意事项

- CSV文件的第一行应该包含字段名：value,rejected,chosen
- 如果CSV文件中缺少某些字段，对应的JSON字段将为空字符串

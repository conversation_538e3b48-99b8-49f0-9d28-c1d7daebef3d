<!doctype html><html lang="zh-cn"><head pia-version="2"><script>window.gfdatav1={"env":"prod","idc":"sinfonline","ver":"1.0.0.323","canary":0,"envName":"prod","region":"CN","runtime":"node","vdc":"sinfonline","vregion":"ChinaSinf-North","extra":{"canaryType":null}}</script><script pia-manifest type="application/json">{"version":"1.0.0.323","cache":{"maxAge":600,"version":"1.0.0.323"},"support":["log2"]}</script><meta charset="utf-8"><title></title><link rel="dns-prefetch" href="//p3.dcarimg.com/"><link rel="dns-prefetch" href="//lf3-motor.dcarstatic.com/"><meta name="screen-orientation" content="portrait"><meta name="x5-orientation" content="portrait"><meta name="format-detection" content="telephone=no"><meta name="referrer" content="origin-when-cross-origin"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no,minimum-scale=1,maximum-scale=1,minimal-ui,viewport-fit=cover"><meta name="apple-mobile-web-app-capable" content="yes"><script>!function(e,r,n,t,s,a,i,o,c,l,d,m,f,p){a="precollect",i="getAttribute",o="addEventListener",l=function(e){(d=[].slice.call(arguments)).push(Date.now(),location.href),(e==a?l.p.a:l.q).push(d)},l.q=[],l.p={a:[]},e[s]=l,(m=document.createElement("script")).src=n+"?bid=usedcar_detect_mobile&globalName="+s,m.crossOrigin=n.indexOf("sdk-web")>0||n.indexOf("slardar-web")>0?"anonymous":"use-credentials",r.getElementsByTagName("head")[0].appendChild(m),o in e&&(l.pcErr=function(r){r=r||e.event,(f=r.target||r.srcElement)instanceof Element||f instanceof HTMLElement?f[i]("integrity")?e[s](a,"sri",f[i]("href")||f[i]("src")):e[s](a,"st",{tagName:f.tagName,url:f[i]("href")||f[i]("src")}):e[s](a,"err",r.error||r.message)},l.pcRej=function(r){r=r||e.event,e[s](a,"err",r.reason||r.detail&&r.detail.reason)},e[o]("error",l.pcErr,!0),e[o]("unhandledrejection",l.pcRej,!0)),"PerformanceLongTaskTiming"in e&&((p=l.pp={entries:[]}).observer=new PerformanceObserver((function(e){p.entries=p.entries.concat(e.getEntries())})),p.observer.observe({entryTypes:["longtask","largest-contentful-paint","layout-shift"]}))}(window,document,"https://lf3-short.ibytedapm.com/slardar/fe/sdk-web/browser.cn.js",0,"Slardar")</script><script>window.Slardar("init",function(){var transformFunction=function(options){for(var key in options)Object.prototype.hasOwnProperty.call(options,key)&&("string"==typeof options[key]&&function(){var identifyStartIndex=options[key].indexOf("PIA_START_IDENTIFY"),identifyEndIndex=options[key].indexOf("PIA_END_IDENTIFY"),contentStartIndex=identifyStartIndex+18,content=options[key].slice(contentStartIndex,identifyEndIndex);-1!==identifyStartIndex&&identifyEndIndex>contentStartIndex&&content&&(options[key]=function(){return eval("(".concat(content,")")).apply(void 0,arguments)})}(),"object"==typeof options[key]&&transformFunction(options[key]));return options};return transformFunction({bid:"usedcar_detect_mobile",release:"1.0.0.323",pid:"midas-preview",env:"production",plugins:{action:!0,jsError:{},ajax:{ignoreUrls:["mcs.zijieapi.com"],collectBodyOnError:!0},fetch:{ignoreUrls:["mcs.zijieapi.com"],collectBodyOnError:!0},blankScreen:{rootSelector:"body",autoDetect:!0,threshold:1.5,screenshot:!0}},integrations:[]})}()),window.Slardar("start")</script><script>!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).xss={})}(this,(function(t){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}var r={exports:{}},i={},o={exports:{}},n={};function a(){return{"align-content":!1,"align-items":!1,"align-self":!1,"alignment-adjust":!1,"alignment-baseline":!1,all:!1,"anchor-point":!1,animation:!1,"animation-delay":!1,"animation-direction":!1,"animation-duration":!1,"animation-fill-mode":!1,"animation-iteration-count":!1,"animation-name":!1,"animation-play-state":!1,"animation-timing-function":!1,azimuth:!1,"backface-visibility":!1,background:!0,"background-attachment":!0,"background-clip":!0,"background-color":!0,"background-image":!0,"background-origin":!0,"background-position":!0,"background-repeat":!0,"background-size":!0,"baseline-shift":!1,binding:!1,bleed:!1,"bookmark-label":!1,"bookmark-level":!1,"bookmark-state":!1,border:!0,"border-bottom":!0,"border-bottom-color":!0,"border-bottom-left-radius":!0,"border-bottom-right-radius":!0,"border-bottom-style":!0,"border-bottom-width":!0,"border-collapse":!0,"border-color":!0,"border-image":!0,"border-image-outset":!0,"border-image-repeat":!0,"border-image-slice":!0,"border-image-source":!0,"border-image-width":!0,"border-left":!0,"border-left-color":!0,"border-left-style":!0,"border-left-width":!0,"border-radius":!0,"border-right":!0,"border-right-color":!0,"border-right-style":!0,"border-right-width":!0,"border-spacing":!0,"border-style":!0,"border-top":!0,"border-top-color":!0,"border-top-left-radius":!0,"border-top-right-radius":!0,"border-top-style":!0,"border-top-width":!0,"border-width":!0,bottom:!1,"box-decoration-break":!0,"box-shadow":!0,"box-sizing":!0,"box-snap":!0,"box-suppress":!0,"break-after":!0,"break-before":!0,"break-inside":!0,"caption-side":!1,chains:!1,clear:!0,clip:!1,"clip-path":!1,"clip-rule":!1,color:!0,"color-interpolation-filters":!0,"column-count":!1,"column-fill":!1,"column-gap":!1,"column-rule":!1,"column-rule-color":!1,"column-rule-style":!1,"column-rule-width":!1,"column-span":!1,"column-width":!1,columns:!1,contain:!1,content:!1,"counter-increment":!1,"counter-reset":!1,"counter-set":!1,crop:!1,cue:!1,"cue-after":!1,"cue-before":!1,cursor:!1,direction:!1,display:!0,"display-inside":!0,"display-list":!0,"display-outside":!0,"dominant-baseline":!1,elevation:!1,"empty-cells":!1,filter:!1,flex:!1,"flex-basis":!1,"flex-direction":!1,"flex-flow":!1,"flex-grow":!1,"flex-shrink":!1,"flex-wrap":!1,float:!1,"float-offset":!1,"flood-color":!1,"flood-opacity":!1,"flow-from":!1,"flow-into":!1,font:!0,"font-family":!0,"font-feature-settings":!0,"font-kerning":!0,"font-language-override":!0,"font-size":!0,"font-size-adjust":!0,"font-stretch":!0,"font-style":!0,"font-synthesis":!0,"font-variant":!0,"font-variant-alternates":!0,"font-variant-caps":!0,"font-variant-east-asian":!0,"font-variant-ligatures":!0,"font-variant-numeric":!0,"font-variant-position":!0,"font-weight":!0,grid:!1,"grid-area":!1,"grid-auto-columns":!1,"grid-auto-flow":!1,"grid-auto-rows":!1,"grid-column":!1,"grid-column-end":!1,"grid-column-start":!1,"grid-row":!1,"grid-row-end":!1,"grid-row-start":!1,"grid-template":!1,"grid-template-areas":!1,"grid-template-columns":!1,"grid-template-rows":!1,"hanging-punctuation":!1,height:!0,hyphens:!1,icon:!1,"image-orientation":!1,"image-resolution":!1,"ime-mode":!1,"initial-letters":!1,"inline-box-align":!1,"justify-content":!1,"justify-items":!1,"justify-self":!1,left:!1,"letter-spacing":!0,"lighting-color":!0,"line-box-contain":!1,"line-break":!1,"line-grid":!1,"line-height":!1,"line-snap":!1,"line-stacking":!1,"line-stacking-ruby":!1,"line-stacking-shift":!1,"line-stacking-strategy":!1,"list-style":!0,"list-style-image":!0,"list-style-position":!0,"list-style-type":!0,margin:!0,"margin-bottom":!0,"margin-left":!0,"margin-right":!0,"margin-top":!0,"marker-offset":!1,"marker-side":!1,marks:!1,mask:!1,"mask-box":!1,"mask-box-outset":!1,"mask-box-repeat":!1,"mask-box-slice":!1,"mask-box-source":!1,"mask-box-width":!1,"mask-clip":!1,"mask-image":!1,"mask-origin":!1,"mask-position":!1,"mask-repeat":!1,"mask-size":!1,"mask-source-type":!1,"mask-type":!1,"max-height":!0,"max-lines":!1,"max-width":!0,"min-height":!0,"min-width":!0,"move-to":!1,"nav-down":!1,"nav-index":!1,"nav-left":!1,"nav-right":!1,"nav-up":!1,"object-fit":!1,"object-position":!1,opacity:!1,order:!1,orphans:!1,outline:!1,"outline-color":!1,"outline-offset":!1,"outline-style":!1,"outline-width":!1,overflow:!1,"overflow-wrap":!1,"overflow-x":!1,"overflow-y":!1,padding:!0,"padding-bottom":!0,"padding-left":!0,"padding-right":!0,"padding-top":!0,page:!1,"page-break-after":!1,"page-break-before":!1,"page-break-inside":!1,"page-policy":!1,pause:!1,"pause-after":!1,"pause-before":!1,perspective:!1,"perspective-origin":!1,pitch:!1,"pitch-range":!1,"play-during":!1,position:!1,"presentation-level":!1,quotes:!1,"region-fragment":!1,resize:!1,rest:!1,"rest-after":!1,"rest-before":!1,richness:!1,right:!1,rotation:!1,"rotation-point":!1,"ruby-align":!1,"ruby-merge":!1,"ruby-position":!1,"shape-image-threshold":!1,"shape-outside":!1,"shape-margin":!1,size:!1,speak:!1,"speak-as":!1,"speak-header":!1,"speak-numeral":!1,"speak-punctuation":!1,"speech-rate":!1,stress:!1,"string-set":!1,"tab-size":!1,"table-layout":!1,"text-align":!0,"text-align-last":!0,"text-combine-upright":!0,"text-decoration":!0,"text-decoration-color":!0,"text-decoration-line":!0,"text-decoration-skip":!0,"text-decoration-style":!0,"text-emphasis":!0,"text-emphasis-color":!0,"text-emphasis-position":!0,"text-emphasis-style":!0,"text-height":!0,"text-indent":!0,"text-justify":!0,"text-orientation":!0,"text-overflow":!0,"text-shadow":!0,"text-space-collapse":!0,"text-transform":!0,"text-underline-position":!0,"text-wrap":!0,top:!1,transform:!1,"transform-origin":!1,"transform-style":!1,transition:!1,"transition-delay":!1,"transition-duration":!1,"transition-property":!1,"transition-timing-function":!1,"unicode-bidi":!1,"vertical-align":!1,visibility:!1,"voice-balance":!1,"voice-duration":!1,"voice-family":!1,"voice-pitch":!1,"voice-range":!1,"voice-rate":!1,"voice-stress":!1,"voice-volume":!1,volume:!1,"white-space":!1,widows:!1,width:!0,"will-change":!1,"word-break":!0,"word-spacing":!0,"word-wrap":!0,"wrap-flow":!1,"wrap-through":!1,"writing-mode":!1,"z-index":!1}}var s=/javascript\s*\:/gim;n.whiteList={"align-content":!1,"align-items":!1,"align-self":!1,"alignment-adjust":!1,"alignment-baseline":!1,all:!1,"anchor-point":!1,animation:!1,"animation-delay":!1,"animation-direction":!1,"animation-duration":!1,"animation-fill-mode":!1,"animation-iteration-count":!1,"animation-name":!1,"animation-play-state":!1,"animation-timing-function":!1,azimuth:!1,"backface-visibility":!1,background:!0,"background-attachment":!0,"background-clip":!0,"background-color":!0,"background-image":!0,"background-origin":!0,"background-position":!0,"background-repeat":!0,"background-size":!0,"baseline-shift":!1,binding:!1,bleed:!1,"bookmark-label":!1,"bookmark-level":!1,"bookmark-state":!1,border:!0,"border-bottom":!0,"border-bottom-color":!0,"border-bottom-left-radius":!0,"border-bottom-right-radius":!0,"border-bottom-style":!0,"border-bottom-width":!0,"border-collapse":!0,"border-color":!0,"border-image":!0,"border-image-outset":!0,"border-image-repeat":!0,"border-image-slice":!0,"border-image-source":!0,"border-image-width":!0,"border-left":!0,"border-left-color":!0,"border-left-style":!0,"border-left-width":!0,"border-radius":!0,"border-right":!0,"border-right-color":!0,"border-right-style":!0,"border-right-width":!0,"border-spacing":!0,"border-style":!0,"border-top":!0,"border-top-color":!0,"border-top-left-radius":!0,"border-top-right-radius":!0,"border-top-style":!0,"border-top-width":!0,"border-width":!0,bottom:!1,"box-decoration-break":!0,"box-shadow":!0,"box-sizing":!0,"box-snap":!0,"box-suppress":!0,"break-after":!0,"break-before":!0,"break-inside":!0,"caption-side":!1,chains:!1,clear:!0,clip:!1,"clip-path":!1,"clip-rule":!1,color:!0,"color-interpolation-filters":!0,"column-count":!1,"column-fill":!1,"column-gap":!1,"column-rule":!1,"column-rule-color":!1,"column-rule-style":!1,"column-rule-width":!1,"column-span":!1,"column-width":!1,columns:!1,contain:!1,content:!1,"counter-increment":!1,"counter-reset":!1,"counter-set":!1,crop:!1,cue:!1,"cue-after":!1,"cue-before":!1,cursor:!1,direction:!1,display:!0,"display-inside":!0,"display-list":!0,"display-outside":!0,"dominant-baseline":!1,elevation:!1,"empty-cells":!1,filter:!1,flex:!1,"flex-basis":!1,"flex-direction":!1,"flex-flow":!1,"flex-grow":!1,"flex-shrink":!1,"flex-wrap":!1,float:!1,"float-offset":!1,"flood-color":!1,"flood-opacity":!1,"flow-from":!1,"flow-into":!1,font:!0,"font-family":!0,"font-feature-settings":!0,"font-kerning":!0,"font-language-override":!0,"font-size":!0,"font-size-adjust":!0,"font-stretch":!0,"font-style":!0,"font-synthesis":!0,"font-variant":!0,"font-variant-alternates":!0,"font-variant-caps":!0,"font-variant-east-asian":!0,"font-variant-ligatures":!0,"font-variant-numeric":!0,"font-variant-position":!0,"font-weight":!0,grid:!1,"grid-area":!1,"grid-auto-columns":!1,"grid-auto-flow":!1,"grid-auto-rows":!1,"grid-column":!1,"grid-column-end":!1,"grid-column-start":!1,"grid-row":!1,"grid-row-end":!1,"grid-row-start":!1,"grid-template":!1,"grid-template-areas":!1,"grid-template-columns":!1,"grid-template-rows":!1,"hanging-punctuation":!1,height:!0,hyphens:!1,icon:!1,"image-orientation":!1,"image-resolution":!1,"ime-mode":!1,"initial-letters":!1,"inline-box-align":!1,"justify-content":!1,"justify-items":!1,"justify-self":!1,left:!1,"letter-spacing":!0,"lighting-color":!0,"line-box-contain":!1,"line-break":!1,"line-grid":!1,"line-height":!1,"line-snap":!1,"line-stacking":!1,"line-stacking-ruby":!1,"line-stacking-shift":!1,"line-stacking-strategy":!1,"list-style":!0,"list-style-image":!0,"list-style-position":!0,"list-style-type":!0,margin:!0,"margin-bottom":!0,"margin-left":!0,"margin-right":!0,"margin-top":!0,"marker-offset":!1,"marker-side":!1,marks:!1,mask:!1,"mask-box":!1,"mask-box-outset":!1,"mask-box-repeat":!1,"mask-box-slice":!1,"mask-box-source":!1,"mask-box-width":!1,"mask-clip":!1,"mask-image":!1,"mask-origin":!1,"mask-position":!1,"mask-repeat":!1,"mask-size":!1,"mask-source-type":!1,"mask-type":!1,"max-height":!0,"max-lines":!1,"max-width":!0,"min-height":!0,"min-width":!0,"move-to":!1,"nav-down":!1,"nav-index":!1,"nav-left":!1,"nav-right":!1,"nav-up":!1,"object-fit":!1,"object-position":!1,opacity:!1,order:!1,orphans:!1,outline:!1,"outline-color":!1,"outline-offset":!1,"outline-style":!1,"outline-width":!1,overflow:!1,"overflow-wrap":!1,"overflow-x":!1,"overflow-y":!1,padding:!0,"padding-bottom":!0,"padding-left":!0,"padding-right":!0,"padding-top":!0,page:!1,"page-break-after":!1,"page-break-before":!1,"page-break-inside":!1,"page-policy":!1,pause:!1,"pause-after":!1,"pause-before":!1,perspective:!1,"perspective-origin":!1,pitch:!1,"pitch-range":!1,"play-during":!1,position:!1,"presentation-level":!1,quotes:!1,"region-fragment":!1,resize:!1,rest:!1,"rest-after":!1,"rest-before":!1,richness:!1,right:!1,rotation:!1,"rotation-point":!1,"ruby-align":!1,"ruby-merge":!1,"ruby-position":!1,"shape-image-threshold":!1,"shape-outside":!1,"shape-margin":!1,size:!1,speak:!1,"speak-as":!1,"speak-header":!1,"speak-numeral":!1,"speak-punctuation":!1,"speech-rate":!1,stress:!1,"string-set":!1,"tab-size":!1,"table-layout":!1,"text-align":!0,"text-align-last":!0,"text-combine-upright":!0,"text-decoration":!0,"text-decoration-color":!0,"text-decoration-line":!0,"text-decoration-skip":!0,"text-decoration-style":!0,"text-emphasis":!0,"text-emphasis-color":!0,"text-emphasis-position":!0,"text-emphasis-style":!0,"text-height":!0,"text-indent":!0,"text-justify":!0,"text-orientation":!0,"text-overflow":!0,"text-shadow":!0,"text-space-collapse":!0,"text-transform":!0,"text-underline-position":!0,"text-wrap":!0,top:!1,transform:!1,"transform-origin":!1,"transform-style":!1,transition:!1,"transition-delay":!1,"transition-duration":!1,"transition-property":!1,"transition-timing-function":!1,"unicode-bidi":!1,"vertical-align":!1,visibility:!1,"voice-balance":!1,"voice-duration":!1,"voice-family":!1,"voice-pitch":!1,"voice-range":!1,"voice-rate":!1,"voice-stress":!1,"voice-volume":!1,volume:!1,"white-space":!1,widows:!1,width:!0,"will-change":!1,"word-break":!0,"word-spacing":!0,"word-wrap":!0,"wrap-flow":!1,"wrap-through":!1,"writing-mode":!1,"z-index":!1},n.getDefaultWhiteList=a,n.onAttr=function(t,e,r){},n.onIgnoreAttr=function(t,e,r){},n.safeAttrValue=function(t,e){return s.test(e)?"":e};var l={indexOf:function(t,e){var r,i;if(Array.prototype.indexOf)return t.indexOf(e);for(r=0,i=t.length;r<i;r++)if(t[r]===e)return r;return-1},forEach:function(t,e,r){var i,o;if(Array.prototype.forEach)return t.forEach(e,r);for(i=0,o=t.length;i<o;i++)e.call(r,t[i],i,t)},trim:function(t){return String.prototype.trim?t.trim():t.replace(/(^\s*)|(\s*$)/g,"")},trimRight:function(t){return String.prototype.trimRight?t.trimRight():t.replace(/(\s*$)/g,"")}},c=l,u=n,f=function(t,e){";"!==(t=c.trimRight(t))[t.length-1]&&(t+=";");var r=t.length,i=!1,o=0,n=0,a="";function s(){if(!i){var r=c.trim(t.slice(o,n)),s=r.indexOf(":");if(-1!==s){var l=c.trim(r.slice(0,s)),u=c.trim(r.slice(s+1));if(l){var f=e(o,a.length,l,u,r);f&&(a+=f+"; ")}}}o=n+1}for(;n<r;n++){var l=t[n];if("/"===l&&"*"===t[n+1]){var u=t.indexOf("*/",n+2);if(-1===u)break;o=(n=u+1)+1,i=!1}else"("===l?i=!0:")"===l?i=!1:";"===l?i||s():"\n"===l&&s()}return c.trim(a)};function d(t){return null==t}function g(t){(t=function(t){var e={};for(var r in t)e[r]=t[r];return e}(t||{})).whiteList=t.whiteList||u.whiteList,t.onAttr=t.onAttr||u.onAttr,t.onIgnoreAttr=t.onIgnoreAttr||u.onIgnoreAttr,t.safeAttrValue=t.safeAttrValue||u.safeAttrValue,this.options=t}g.prototype.process=function(t){if(!(t=(t=t||"").toString()))return"";var e=this.options,r=e.whiteList,i=e.onAttr,o=e.onIgnoreAttr,n=e.safeAttrValue;return f(t,(function(t,e,a,s,l){var c=r[a],u=!1;if(!0===c?u=c:"function"==typeof c?u=c(s):c instanceof RegExp&&(u=c.test(s)),!0!==u&&(u=!1),s=n(a,s)){var f,g={position:e,sourcePosition:t,source:l,isWhite:u};return u?d(f=i(a,s,g))?a+":"+s:f:d(f=o(a,s,g))?void 0:f}}))};var p=g;!function(t,e){var r=n,i=p;for(var o in(e=t.exports=function(t,e){return new i(e).process(t)}).FilterCSS=i,r)e[o]=r[o];"undefined"!=typeof window&&(window.filterCSS=t.exports)}(o,o.exports);var h={indexOf:function(t,e){var r,i;if(Array.prototype.indexOf)return t.indexOf(e);for(r=0,i=t.length;r<i;r++)if(t[r]===e)return r;return-1},forEach:function(t,e,r){var i,o;if(Array.prototype.forEach)return t.forEach(e,r);for(i=0,o=t.length;i<o;i++)e.call(r,t[i],i,t)},trim:function(t){return String.prototype.trim?t.trim():t.replace(/(^\s*)|(\s*$)/g,"")},spaceIndex:function(t){var e=/\s|\n|\t/.exec(t);return e?e.index:-1}},m=o.exports.FilterCSS,b=o.exports.getDefaultWhiteList,v=h;var y=new m;function w(t){return t.replace(x,"&lt;").replace(k,"&gt;")}var x=/</g,k=/>/g,A=/"/g,S=/&quot;/g,L=/&#([a-zA-Z0-9]*);?/gim,j=/&colon;?/gim,T=/&newline;?/gim,C=/((j\s*a\s*v\s*a|v\s*b|l\s*i\s*v\s*e)\s*s\s*c\s*r\s*i\s*p\s*t\s*|m\s*o\s*c\s*h\s*a):/gi,z=/e\s*x\s*p\s*r\s*e\s*s\s*s\s*i\s*o\s*n\s*\(.*/gi,I=/u\s*r\s*l\s*\(.*/gi;function N(t){return t.replace(A,"&quot;")}function W(t){return t.replace(S,'"')}function F(t){return t.replace(L,(function(t,e){return"x"===e[0]||"X"===e[0]?String.fromCharCode(parseInt(e.substr(1),16)):String.fromCharCode(parseInt(e,10))}))}function X(t){return t.replace(j,":").replace(T," ")}function Z(t){for(var e="",r=0,i=t.length;r<i;r++)e+=t.charCodeAt(r)<32?" ":t.charAt(r);return v.trim(e)}function E(t){return Z(t=X(t=F(t=W(t))))}function O(t){return w(t=N(t))}i.whiteList={a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height"],ins:["datetime"],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]},i.getDefaultWhiteList=function(){return{a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height"],ins:["datetime"],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]}},i.onTag=function(t,e,r){},i.onIgnoreTag=function(t,e,r){},i.onTagAttr=function(t,e,r){},i.onIgnoreTagAttr=function(t,e,r){},i.safeAttrValue=function(t,e,r,i){if(r=E(r),"href"===e||"src"===e){if("#"===(r=v.trim(r)))return"#";if("http://"!==r.substr(0,7)&&"https://"!==r.substr(0,8)&&"mailto:"!==r.substr(0,7)&&"tel:"!==r.substr(0,4)&&"data:image/"!==r.substr(0,11)&&"ftp://"!==r.substr(0,6)&&"./"!==r.substr(0,2)&&"../"!==r.substr(0,3)&&"#"!==r[0]&&"/"!==r[0])return""}else if("background"===e){if(C.lastIndex=0,C.test(r))return""}else if("style"===e){if(z.lastIndex=0,z.test(r))return"";if(I.lastIndex=0,I.test(r)&&(C.lastIndex=0,C.test(r)))return"";!1!==i&&(r=(i=i||y).process(r))}return O(r)},i.escapeHtml=w,i.escapeQuote=N,i.unescapeQuote=W,i.escapeHtmlEntities=F,i.escapeDangerHtml5Entities=X,i.clearNonPrintableCharacter=Z,i.friendlyAttrValue=E,i.escapeAttrValue=O,i.onIgnoreTagStripAll=function(){return""},i.StripTagBody=function(t,e){"function"!=typeof e&&(e=function(){});var r=!Array.isArray(t),i=[],o=!1;return{onIgnoreTag:function(n,a,s){if(function(e){return!!r||-1!==v.indexOf(t,e)}(n)){if(s.isClosing){var l="[/removed]",c=s.position+10;return i.push([!1!==o?o:s.position,c]),o=!1,l}return o||(o=s.position),"[removed]"}return e(n,a,s)},remove:function(t){var e="",r=0;return v.forEach(i,(function(i){e+=t.slice(r,i[0]),r=i[1]})),e+=t.slice(r)}}},i.stripCommentTag=function(t){for(var e="",r=0;r<t.length;){var i=t.indexOf("\x3c!--",r);if(-1===i){e+=t.slice(r);break}e+=t.slice(r,i);var o=t.indexOf("--\x3e",i);if(-1===o)break;r=o+3}return e},i.stripBlankChar=function(t){var e=t.split("");return(e=e.filter((function(t){var e=t.charCodeAt(0);return!(127===e||e<=31&&10!==e&&13!==e)}))).join("")},i.cssFilter=y,i.getDefaultCSSWhiteList=b;var J={},Y=h;function D(t){var e,r=Y.spaceIndex(t);return e=-1===r?t.slice(1,-1):t.slice(1,r+1),"/"===(e=Y.trim(e).toLowerCase()).slice(0,1)&&(e=e.slice(1)),"/"===e.slice(-1)&&(e=e.slice(0,-1)),e}function B(t){return"</"===t.slice(0,2)}var H=/[^a-zA-Z0-9\\_:.-]/gim;function V(t,e){for(;e<t.length;e++){var r=t[e];if(" "!==r)return"="===r?e:-1}}function P(t,e){for(;e<t.length;e++){var r=t[e];if(" "!==r)return"'"===r||'"'===r?e:-1}}function R(t,e){for(;e>0;e--){var r=t[e];if(" "!==r)return"="===r?e:-1}}function _(t){return function(t){return'"'===t[0]&&'"'===t[t.length-1]||"'"===t[0]&&"'"===t[t.length-1]}(t)?t.substr(1,t.length-2):t}J.parseTag=function(t,e,r){var i="",o=0,n=!1,a=!1,s=0,l=t.length,c="",u="";t:for(s=0;s<l;s++){var f=t.charAt(s);if(!1===n){if("<"===f){n=s;continue}}else if(!1===a){if("<"===f){i+=r(t.slice(o,s)),n=s,o=s;continue}if(">"===f||s===l-1){i+=r(t.slice(o,n)),c=D(u=t.slice(n,s+1)),i+=e(n,i.length,c,u,B(u)),o=s+1,n=!1;continue}if('"'===f||"'"===f)for(var d=1,g=t.charAt(s-d);""===g.trim()||"="===g;){if("="===g){a=f;continue t}g=t.charAt(s-++d)}}else if(f===a){a=!1;continue}}return o<l&&(i+=r(t.substr(o))),i},J.parseAttr=function(t,e){var r=0,i=0,o=[],n=!1,a=t.length;function s(t,r){if(!((t=(t=Y.trim(t)).replace(H,"").toLowerCase()).length<1)){var i=e(t,r||"");i&&o.push(i)}}for(var l=0;l<a;l++){var c,u=t.charAt(l);if(!1!==n||"="!==u)if(!1===n||l!==i){if(/\s|\n|\t/.test(u)){if(t=t.replace(/\s|\n|\t/g," "),!1===n){if(-1===(c=V(t,l))){s(Y.trim(t.slice(r,l))),n=!1,r=l+1;continue}l=c-1;continue}if(-1===(c=R(t,l-1))){s(n,_(Y.trim(t.slice(r,l)))),n=!1,r=l+1;continue}}}else{if(-1===(c=t.indexOf(u,l+1)))break;s(n,Y.trim(t.slice(i+1,c))),n=!1,r=(l=c)+1}else n=t.slice(r,l),r=l+1,i='"'===t.charAt(r)||"'"===t.charAt(r)?r:P(t,l+1)}return r<t.length&&(!1===n?s(t.slice(r)):s(n,_(Y.trim(t.slice(r))))),Y.trim(o.join(" "))};var q=o.exports.FilterCSS,G=i,M=J,U=M.parseTag,Q=M.parseAttr,K=h;function $(t){return null==t}function tt(t){(t=function(t){var e={};for(var r in t)e[r]=t[r];return e}(t||{})).stripIgnoreTag&&(t.onIgnoreTag&&console.error('Notes: cannot use these two options "stripIgnoreTag" and "onIgnoreTag" at the same time'),t.onIgnoreTag=G.onIgnoreTagStripAll),t.whiteList||t.allowList?t.whiteList=function(t){var e={};for(var r in t)Array.isArray(t[r])?e[r.toLowerCase()]=t[r].map((function(t){return t.toLowerCase()})):e[r.toLowerCase()]=t[r];return e}(t.whiteList||t.allowList):t.whiteList=G.whiteList,t.onTag=t.onTag||G.onTag,t.onTagAttr=t.onTagAttr||G.onTagAttr,t.onIgnoreTag=t.onIgnoreTag||G.onIgnoreTag,t.onIgnoreTagAttr=t.onIgnoreTagAttr||G.onIgnoreTagAttr,t.safeAttrValue=t.safeAttrValue||G.safeAttrValue,t.escapeHtml=t.escapeHtml||G.escapeHtml,this.options=t,!1===t.css?this.cssFilter=!1:(t.css=t.css||{},this.cssFilter=new q(t.css))}tt.prototype.process=function(t){if(!(t=(t=t||"").toString()))return"";var e=this.options,r=e.whiteList,i=e.onTag,o=e.onIgnoreTag,n=e.onTagAttr,a=e.onIgnoreTagAttr,s=e.safeAttrValue,l=e.escapeHtml,c=this.cssFilter;e.stripBlankChar&&(t=G.stripBlankChar(t)),e.allowCommentTag||(t=G.stripCommentTag(t));var u=!1;e.stripIgnoreTagBody&&(u=G.StripTagBody(e.stripIgnoreTagBody,o),o=u.onIgnoreTag);var f=U(t,(function(t,e,u,f,d){var g={sourcePosition:t,position:e,isClosing:d,isWhite:Object.prototype.hasOwnProperty.call(r,u)},p=i(u,f,g);if(!$(p))return p;if(g.isWhite){if(g.isClosing)return"</"+u+">";var h=function(t){var e=K.spaceIndex(t);if(-1===e)return{html:"",closing:"/"===t[t.length-2]};var r="/"===(t=K.trim(t.slice(e+1,-1)))[t.length-1];return r&&(t=K.trim(t.slice(0,-1))),{html:t,closing:r}}(f),m=r[u],b=Q(h.html,(function(t,e){var r=-1!==K.indexOf(m,t),i=n(u,t,e,r);return $(i)?r?(e=s(u,t,e,c))?t+'="'+e+'"':t:$(i=a(u,t,e,r))?void 0:i:i}));return f="<"+u,b&&(f+=" "+b),h.closing&&(f+=" /"),f+">"}return $(p=o(u,f,g))?l(f):p}),l);return u&&(f=u.remove(f)),f};var et=tt;function rt(t,e,r){if(r||2===arguments.length)for(var i,o=0,n=e.length;o<n;o++)!i&&o in e||(i||(i=Array.prototype.slice.call(e,0,o)),i[o]=e[o]);return t.concat(i||Array.prototype.slice.call(e))}!function(t,e){var r=i,o=J,n=et;function a(t,e){return new n(e).process(t)}(e=t.exports=a).filterXSS=a,e.FilterXSS=n,function(){for(var t in r)e[t]=r[t];for(var i in o)e[i]=o[i]}(),"undefined"!=typeof window&&(window.filterXSS=t.exports),"undefined"!=typeof self&&"undefined"!=typeof DedicatedWorkerGlobalScope&&self instanceof DedicatedWorkerGlobalScope&&(self.filterXSS=t.exports)}(r,r.exports);var it=function(t){return-1===t.indexOf("&#")?t.trim().toLowerCase():t.trim().replace(/&#(?:(x)([0-9a-f]+)|([0-9]+));?/gi,(function(t,e,r,i){return String.fromCharCode(e?parseInt(r,16):parseInt(i))})).toLowerCase()};function ot(t){if(void 0===t&&(t=""),"string"!=typeof t)return!0;var e=(t=it(t)).replace(/(\t|\n|\r)/g,"");if(t!==e)return!1;if(/javascript:/.test(t)&&-1!==t.indexOf("javascript:"))return!1;if(/^javascript:/i.test(t)){var r=t.slice(11).replace(/\s/g,"").trim();return!!["void","void(0)","void0","false","undefined",";"].some((function(t){return t===r}))}return!0}var nt=function(t){return ot(t)?t:"#"};function at(t,r,i){if(void 0===t&&(t=""),void 0===r&&(r=[]),"string"!=typeof t)return!0;if(!ot(t=it(t)))return!1;var o=function(t){var e=t.match(/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/)||[];return{url:e[0],scheme:e[1],slash:e[2],host:e[3],port:e[4],path:e[5],query:e[6],hash:e[7]}}(t),n=o.scheme,a=o.host;return i?Boolean(i(t)):!["http","https","file"].includes(n)||("object"===("undefined"==typeof window?"undefined":e(window))&&window&&(r=rt(rt([],r,!0),[location.host],!1)),r.some((function(t){return!!(t instanceof RegExp&&t.test(a))||t===a})))}var st,lt,ct=at,ut=at,ft="function"==typeof atob,dt="function"==typeof btoa,gt="function"==typeof Buffer,pt="function"==typeof TextDecoder?new TextDecoder:void 0,ht="function"==typeof TextEncoder?new TextEncoder:void 0,mt=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),bt=(st={},mt.forEach((function(t,e){return st[t]=e})),st),vt=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,yt=String.fromCharCode.bind(String),wt="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):function(t){return new Uint8Array(Array.prototype.slice.call(t,0))},xt=function(t){return t.replace(/[^A-Za-z0-9\+\/]/g,"")},kt=dt?function(t){return btoa(t)}:gt?function(t){return Buffer.from(t,"binary").toString("base64")}:function(t){for(var e,r,i,o,n="",a=t.length%3,s=0;s<t.length;){if((r=t.charCodeAt(s++))>255||(i=t.charCodeAt(s++))>255||(o=t.charCodeAt(s++))>255)throw new TypeError("invalid character found");n+=mt[(e=r<<16|i<<8|o)>>18&63]+mt[e>>12&63]+mt[e>>6&63]+mt[63&e]}return a?n.slice(0,a-3)+"===".substring(a):n},At=gt?function(t){return Buffer.from(t).toString("base64")}:function(t){for(var e=[],r=0,i=t.length;r<i;r+=4096)e.push(yt.apply(null,t.subarray(r,r+4096)));return kt(e.join(""))},St=function(t){if(t.length<2)return(e=t.charCodeAt(0))<128?t:e<2048?yt(192|e>>>6)+yt(128|63&e):yt(224|e>>>12&15)+yt(128|e>>>6&63)+yt(128|63&e);var e=65536+1024*(t.charCodeAt(0)-55296)+(t.charCodeAt(1)-56320);return yt(240|e>>>18&7)+yt(128|e>>>12&63)+yt(128|e>>>6&63)+yt(128|63&e)},Lt=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,jt=gt?function(t){return Buffer.from(t,"utf8").toString("base64")}:ht?function(t){return At(ht.encode(t))}:function(t){return kt(t.replace(Lt,St))},Tt=function(t){return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?function(t){return t.replace(/=/g,"").replace(/[+\/]/g,(function(t){return"+"==t?"-":"_"}))}(jt(t)):jt(t)},Ct=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,zt=function(t){switch(t.length){case 4:var e=((7&t.charCodeAt(0))<<18|(63&t.charCodeAt(1))<<12|(63&t.charCodeAt(2))<<6|63&t.charCodeAt(3))-65536;return yt(55296+(e>>>10))+yt(56320+(1023&e));case 3:return yt((15&t.charCodeAt(0))<<12|(63&t.charCodeAt(1))<<6|63&t.charCodeAt(2));default:return yt((31&t.charCodeAt(0))<<6|63&t.charCodeAt(1))}},It=ft?function(t){return atob(xt(t))}:gt?function(t){return Buffer.from(t,"base64").toString("binary")}:function(t){if(t=t.replace(/\s+/g,""),!vt.test(t))throw new TypeError("malformed base64.");t+="==".slice(2-(3&t.length));for(var e,r,i,o="",n=0;n<t.length;)e=bt[t.charAt(n++)]<<18|bt[t.charAt(n++)]<<12|(r=bt[t.charAt(n++)])<<6|(i=bt[t.charAt(n++)]),o+=64===r?yt(e>>16&255):64===i?yt(e>>16&255,e>>8&255):yt(e>>16&255,e>>8&255,255&e);return o},Nt=gt?function(t){return wt(Buffer.from(t,"base64"))}:function(t){return wt(It(t).split("").map((function(t){return t.charCodeAt(0)})))},Wt=gt?function(t){return Buffer.from(t,"base64").toString("utf8")}:pt?function(t){return pt.decode(Nt(t))}:function(t){return It(t).replace(Ct,zt)},Ft=function(t){return Wt(function(t){return xt(t.replace(/[-_]/g,(function(t){return"-"==t?"+":"/"})))}(t))},Xt=function(t){return t&&t.Math==Math&&t},Zt=Xt("object"===("undefined"==typeof globalThis?"undefined":e(globalThis))&&globalThis)||Xt("object"===("undefined"==typeof window?"undefined":e(window))&&window)||Xt("object"===("undefined"==typeof self?"undefined":e(self))&&self)||Xt("object"===("undefined"==typeof global?"undefined":e(global))&&global)||Function("return this")(),Et={a:["target","title","spellcheck","rel"],canvas:[],abbr:["title"],address:[],area:["shape","coords","alt"],article:[],aside:[],audio:["autoplay","controls","loop","preload"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:["dir"],dl:[],dt:[],em:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["alt","title","width","height","decoding"],ins:["datetime"],li:[],mark:[],nav:[],ol:["start"],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],sup:[],delete:[],form:[],strong:[],mask:["maskunits","x","y","width","height","fill"],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],wbr:[],video:["autoplay","controls","loop","preload","height","width"],svg:["viewBox","version","xmlns","fill","width","height","stroke","stroke-width","style"],path:["d","fill","opacity","stroke","p-id","fill-rule","clip-rule","stroke-width","stroke-linecap","stroke-linejoin","fill-opacity","mask"],rect:["x","y","width","height","fill","stroke","rx"],g:[]},Ot={onIgnoreTagAttr:function(t,e,r){return t&&["href","src"].indexOf(e)>-1?Ot.domainWhiteList&&Array.isArray(Ot.domainWhiteList)&&Ot.domainWhiteList.length>0&&!ct(r,rt([],Ot.domainWhiteList,!0))?"":"".concat(e,'="').concat(nt(r),'"'):t&&(["style","class","id"].indexOf(e)>-1||e.indexOf("data-")>-1)?"".concat(e,'="').concat(r,'"'):void(Ot.xssLog&&Ot.xssLog({type:"attr",tagName:t,attrName:e,value:r}))},onIgnoreTag:function(t,e){if("style"===t)return e;Ot.xssLog&&Ot.xssLog({type:"tag",tagName:t,value:e})},whiteList:Et,mergeWhiteList:function(t){for(var e={},r=0,i=Object.keys(Et);r<i.length;r++)e[a=i[r]]=Array.from(Et[a]);for(var o=0,n=Object.keys(t);o<n.length;o++){var a;e[a=n[o]]=a in Et?Et[a].concat(t[a]):Array.from(t[a])}return e},setWhiteList:function(t){for(var e=0,r=Object.keys(t);e<r.length;e++){var i=r[e];this.whiteList[i]=i in Et?Et[i].concat(t[i]):Array.from(t[i])}}},Jt=new(function(){function t(){this.batchData=[],this.uniqKeys=new Set,this.timeout=2e3,this.lock=!1}return t.prototype.upload=function(){var t=this,r=function(){if(Ot.reportUrl)return Ot.reportUrl;var t,e={cn:Ft("aHR0cHM6Ly9tb24uemlqaWVhcGkuY29tL21vbml0b3JfYnJvd3Nlci9jb2xsZWN0L2JhdGNoL3NlY3VyaXR5Lz9iaWQ9"),boe:Ft("aHR0cHM6Ly9tb24uemlqaWVhcGkuY29tL21vbml0b3JfYnJvd3Nlci9jb2xsZWN0L2JhdGNoL3NlY3VyaXR5Lz9iaWQ9"),ttp:Ft("aHR0cHM6Ly9tb24udXMudGlrdG9rdi5jb20vbW9uaXRvcl9icm93c2VyL2NvbGxlY3QvYmF0Y2gvc2VjdXJpdHkvP2JpZD0="),va:Ft("aHR0cHM6Ly9tb24tdmEuYnl0ZW92ZXJzZWEuY29tL21vbml0b3JfYnJvd3Nlci9jb2xsZWN0L2JhdGNoL3NlY3VyaXR5Lz9iaWQ9"),maliva:Ft("aHR0cHM6Ly9tb24tdmEuYnl0ZW92ZXJzZWEuY29tL21vbml0b3JfYnJvd3Nlci9jb2xsZWN0L2JhdGNoL3NlY3VyaXR5Lz9iaWQ9"),sg:Ft("aHR0cHM6Ly9tb24tdmEuYnl0ZW92ZXJzZWEuY29tL21vbml0b3JfYnJvd3Nlci9jb2xsZWN0L2JhdGNoL3NlY3VyaXR5Lz9iaWQ9"),boei18n:Ft("aHR0cHM6Ly9tb24tdmEuYnl0ZW92ZXJzZWEuY29tL21vbml0b3JfYnJvd3Nlci9jb2xsZWN0L2JhdGNoL3NlY3VyaXR5Lz9iaWQ9")}[Ot.region?Ot.region:((null===(t=null==Zt?void 0:Zt.gfdatav1)||void 0===t?void 0:t.region)||"cn").toLowerCase()];return e?e+function(){var t,e;if(Ot.bid)return Ot.bid;var r=Zt;if(r&&r._xssBid)return r._xssBid;if(r&&r.slardar&&"function"==typeof r.slardar.config){var i=(r.slardar.config()||{}).bid;if(i)return i}if(r&&r.Slardar&&"function"==typeof r.Slardar.config){var o=(r.Slardar.config()||{}).bid;if(o)return o}return(null===(e=null===(t=null==r?void 0:r.Slardar)||void 0===t?void 0:t._baseParams)||void 0===e?void 0:e.bid)||"argus"}():void 0}();!this.lock&&r&&0!==this.batchData.length&&"object"===("undefined"==typeof window?"undefined":e(window))&&(this.lock=!0,setTimeout((function(){var e=t.batchData.slice(0,100);t.batchData=t.batchData.slice(100),window.fetch(r,{method:"post",body:JSON.stringify(e),headers:{"Content-Type":"application/json"}}).catch((function(t){console.warn("xss defense report error",t)})),t.lock=!1,t.upload()}),this.timeout))},t.prototype.generateKey=function(t){return["type","url","sourceText"].map((function(e){return t[e]})).join("___")},t.prototype.push=function(t){var e=this.generateKey(t);this.uniqKeys.has(e)||(this.uniqKeys.add(e),this.batchData.push(t),this.upload())},t}()),Yt=function(t){if("object"===("undefined"==typeof window?"undefined":e(window))&&window.fetch){var r={age:Math.floor(Date.now()),type:"xss",url:window.location.href,body:t,"user-agent":""};Jt.push(r)}},Dt=function(t){var e=t.reportOnly,r=void 0===e||e,i=t.block;return r&&"all"===r?"report":i?"enforce":r?"report":"enforce"},Bt=function(t){return"string"==typeof t?t.replace(/'/g,'"').replace('=""',"").replace(/\s+/g,"").toLowerCase():""},Ht=function(t){return function(e,r,i){if(!e||"string"!=typeof e)return e;var o=t(e,r);if(Bt(o)===Bt(e))return e;if(!i)return o;var n=i.logType,a=Dt(i);return Yt({type:n,disposition:a,sourceText:Tt(e),filterText:Tt(o)}),"enforce"===a?o:e}},Vt=Ht(r.exports.filterXSS),Pt=Ht((function(t){return new r.exports.FilterXSS(Ot).process(t)})),Rt=(lt=nt,function(t,e,r){var i=lt(t);if(i===t)return t;var o=e||r||{};if(!o)return i;var n=o.logType,a=Dt(r);return Yt({type:n,disposition:a,sourceText:Tt(t),filterText:Tt(i)}),"enforce"===a?i:t}),_t=Zt._xssProject||{},qt="2.0.80",Gt={FilterXSS:r.exports.FilterXSS,version:qt,filterXSS:Vt,_filterXSS:Pt,filterUrl:Rt,Config:Ot,project:_t,setProjectName:function(t){_t[t]=this,Zt._xssProjectName=t}};_t[qt]=Gt,Zt.globalThis=Zt,Zt.getFilterXss=function(){return void 0!==this._xssProjectName?this._xssProject[this._xssProjectName]:Gt},Zt.xss=Gt,Zt.isSafeUrl=ct,Zt.isSafeDomain=ut,Zt.isSafeProtocol=ot,Zt._xssProject=_t,Zt._xssProjectName&&(_t[Zt._xssProjectName]=Gt);var Mt=Gt.setProjectName.bind(Gt);t.Config=Ot,t.FilterXSS=r.exports.FilterXSS,t._filterXSS=Pt,t.filterUrl=Rt,t.filterXSS=Vt,t.isSafeDomain=ut,t.isSafeProtocol=ot,t.isSafeUrl=ct,t.project=_t,t.setProjectName=Mt,Object.defineProperty(t,"__esModule",{value:!0})}))</script><link rel="dns-prefetch" href="//ipolyfill.edge-byted.com"><link rel="preconnect" href="//ipolyfill.edge-byted.com"><link rel="preload" as="script" href="//ipolyfill.edge-byted.com/0.0.18/polyfill.min.js"><meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1"><meta http-equiv="x-dns-prefetch-control" content="on"><meta name="format-detection" content="telephone=no,address=no,email=no"><meta name="referrer" content="no-referrer"><link href="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/resource/css/style.f6631bb6.css" rel="stylesheet"><link href="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/resource/css/lib-xgplayer.a81713bd.css" rel="stylesheet"><link href="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/resource/css/725.2bf37ec4.css" rel="stylesheet"><link href="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/resource/css/midas-preview.7ac1d7f4.css" rel="stylesheet"></head><body mui-theme="mct" close-dark="true"><div id="root"></div><script src="//ipolyfill.edge-byted.com/0.0.18/polyfill.min.js" defer="defer"></script><script defer="defer" src="https://lf6-cdn2-tos.bytegoofy.com/motor/feoffline/gando_libs/lottie-web_v5.6.4.js"></script><script defer="defer">!function(){"use strict";var e,t,r,n,o,i={},u={};function c(e){var t=u[e];if(void 0!==t)return t.exports;var r=u[e]={id:e,loaded:!1,exports:{}};return i[e].call(r.exports,r,r.exports,c),r.loaded=!0,r.exports}c.m=i,e=[],c.O=function(t,r,n,o){if(!r){var i=1/0;for(d=0;d<e.length;d++){r=e[d][0],n=e[d][1],o=e[d][2];for(var u=!0,a=0;a<r.length;a++)(!1&o||i>=o)&&Object.keys(c.O).every((function(e){return c.O[e](r[a])}))?r.splice(a--,1):(u=!1,o<i&&(i=o));if(u){e.splice(d--,1);var f=n();void 0!==f&&(t=f)}}return t}o=o||0;for(var d=e.length;d>0&&e[d-1][2]>o;d--)e[d]=e[d-1];e[d]=[r,n,o]},c.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return c.d(t,{a:t}),t},r=Object.getPrototypeOf?function(e){return Object.getPrototypeOf(e)}:function(e){return e.__proto__},c.t=function(e,n){if(1&n&&(e=this(e)),8&n)return e;if("object"==typeof e&&e){if(4&n&&e.__esModule)return e;if(16&n&&"function"==typeof e.then)return e}var o=Object.create(null);c.r(o);var i={};t=t||[null,r({}),r([]),r(r)];for(var u=2&n&&e;"object"==typeof u&&!~t.indexOf(u);u=r(u))Object.getOwnPropertyNames(u).forEach((function(t){i[t]=function(){return e[t]}}));return i.default=function(){return e},c.d(o,i),o},c.d=function(e,t){for(var r in t)c.o(t,r)&&!c.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},c.f={},c.e=function(e){return Promise.all(Object.keys(c.f).reduce((function(t,r){return c.f[r](e,t),t}),[]))},c.u=function(e){return"resource/js/async/"+e+"."+{70:"b8d1e32f",88:"afb3abfc",117:"5ecf36de",133:"2114b096",271:"d2a974f8",287:"532abab5",307:"02ed3337",356:"f8eec4ce",358:"4a360843",519:"9413439f",537:"f3aa4447",603:"6da84f42",636:"fcfbf025",704:"5bf88ff6",731:"d5468393",819:"47f2484f",906:"1fdada05",918:"11a52a4b",999:"a38444d3"}[e]+".js"},c.miniCssF=function(e){},c.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),c.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n={},o="usedcar_detect_midas:",c.l=function(e,t,r,i){if(n[e])n[e].push(t);else{var u,a;if(void 0!==r)for(var f=document.getElementsByTagName("script"),d=0;d<f.length;d++){var l=f[d];if(l.getAttribute("src")==e||l.getAttribute("data-webpack")==o+r){u=l;break}}u||(a=!0,(u=document.createElement("script")).charset="utf-8",u.timeout=120,c.nc&&u.setAttribute("nonce",c.nc),u.setAttribute("data-webpack",o+r),u.src=e),n[e]=[t];var s=function(t,r){u.onerror=u.onload=null,clearTimeout(b);var o=n[e];if(delete n[e],u.parentNode&&u.parentNode.removeChild(u),o&&o.forEach((function(e){return e(r)})),t)return t(r)},b=setTimeout(s.bind(null,void 0,{type:"timeout",target:u}),12e4);u.onerror=s.bind(null,u.onerror),u.onload=s.bind(null,u.onload),a&&document.head.appendChild(u)}},c.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e},c.p="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/",function(){var e={691:0,314:0};c.f.j=function(t,r){var n=c.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else if(/^(314|691)$/.test(t))e[t]=0;else{var o=new Promise((function(r,o){n=e[t]=[r,o]}));r.push(n[2]=o);var i=c.p+c.u(t),u=new Error;c.l(i,(function(r){if(c.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var o=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;u.message="Loading chunk "+t+" failed.\n("+o+": "+i+")",u.name="ChunkLoadError",u.type=o,u.request=i,n[1](u)}}),"chunk-"+t,t)}},c.O.j=function(t){return 0===e[t]};var t=function(t,r){var n,o,i=r[0],u=r[1],a=r[2],f=0;if(i.some((function(t){return 0!==e[t]}))){for(n in u)c.o(u,n)&&(c.m[n]=u[n]);if(a)var d=a(c)}for(t&&t(r);f<i.length;f++)o=i[f],c.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return c.O(d)},r=self.webpackChunkusedcar_detect_midas=self.webpackChunkusedcar_detect_midas||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))}()}()</script><script defer="defer" src="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/resource/js/lib-xgplayer.92ef6703.js"></script><script defer="defer" src="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/resource/js/lib-formily.f05b25b0.js"></script><script defer="defer" src="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/resource/js/lib-motor.07867090.js"></script><script defer="defer" src="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/resource/js/lib-bridge.bd987c9e.js"></script><script defer="defer" src="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/resource/js/lib-swiper.02a5f355.js"></script><script defer="defer" src="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/resource/js/lib-react.89c81802.js"></script><script defer="defer" src="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/resource/js/lib-helpers.d6a4e3c1.js"></script><script defer="defer" src="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/resource/js/725.360c5351.js"></script><script defer="defer" src="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/resource/js/922.80a270e2.js"></script><script defer="defer" src="//lf3-motor.dcarstatic.com/obj/motor-fe-static/motor/feoffline/usedcar_detect_midas/resource/js/midas-preview.08866bff.js"></script><script>!function(){"use strict";var e={link:HTMLLinkElement,script:HTMLScriptElement,img:HTMLImageElement};function t(e,t){for(var n="",r=0;r<t.length;r++)if(-1!==e.indexOf(t[r])){n=t[r];break}return n||e}var n={max:3,type:Object.keys(e),domain:[],crossOrigin:!1};function r(t,n){var r,i=n.target,a=null===(r=i.tagName)||void 0===r?void 0:r.toLocaleLowerCase(),o=t.type,s=function(e){return e instanceof HTMLScriptElement||e instanceof HTMLImageElement?e.src:e instanceof HTMLLinkElement?e.href:null}(i);return!!(a&&-1!==o.indexOf(a)&&e[a]&&i instanceof e[a]&&s)&&{target:i,tagName:a,url:s}}function i(e,n){var i=r(e,n);if(!1!==i){var a=i.target,o=i.tagName,s=i.url,c=e.test;if(c){if("string"==typeof c){var m=new RegExp(c);c=function(e){return m.test(e)}}if("function"!=typeof c||!c(s))return}var l=t(s,e.domain);if(!(e.domain&&e.domain.length>0&&-1===e.domain.indexOf(l))){var d=Number(a.dataset.rsbuildRetryTimes)||0;if(d!==e.max){var u=function(e,n){var r=t(e,n),i=n.indexOf(r);return n[(i+1)%n.length]||e}(l,e.domain),f=Boolean(a.dataset.rsbuildAsync)||a.async||a.defer,y={url:s.replace(l,u),times:d+1,crossOrigin:e.crossOrigin,isAsync:f},g=function(e,t){var n=!0===t.crossOrigin?"anonymous":t.crossOrigin,r=n?'crossorigin="'.concat(n,'"'):"",i=t.times?'data-rsbuild-retry-times="'.concat(t.times,'"'):"",a=t.isAsync?"data-rsbuild-async":"";if(e instanceof HTMLScriptElement){var o=document.createElement("script");return o.src=t.url,n&&(o.crossOrigin=n),t.times&&(o.dataset.rsbuildRetryTimes=String(t.times)),t.isAsync&&(o.dataset.rsbuildAsync=""),{element:o,str:'<script src="'.concat(t.url,'" ').concat(r," ").concat(i," ").concat(a,"><\/script>")}}if(e instanceof HTMLLinkElement){var s=document.createElement("link");return s.rel=e.rel||"stylesheet",e.as&&(s.as=e.as),s.href=t.url,n&&(s.crossOrigin=n),t.times&&(s.dataset.rsbuildRetryTimes=String(t.times)),{element:s,str:'<link rel="'.concat(s.rel,'" href="').concat(t.url,'" ').concat(r," ").concat(i," ").concat(s.as?'as="'.concat(s.as,'"'):"","></link>")}}}(a,y);if(e.onRetry&&"function"==typeof e.onRetry){var p={times:d,domain:l,url:s,tagName:o};e.onRetry(p)}!function(e,t,n){e instanceof HTMLScriptElement&&(n.isAsync?document.body.appendChild(t.element):document.write(t.str)),e instanceof HTMLLinkElement&&document.getElementsByTagName("head")[0].appendChild(t.element),e instanceof HTMLImageElement&&(e.src=n.url,e.dataset.rsbuildRetryTimes=String(n.times))}(a,g,y)}else if("function"==typeof e.onFail){var v={times:d,domain:l,url:s,tagName:o};e.onFail(v)}}}}!function(e){var a=Object.assign({},n,e);Array.isArray(a.type)&&0!==a.type.length||(a.type=n.type),Array.isArray(a.domain)&&0!==a.domain.length||(a.domain=n.domain),Array.isArray(a.domain)&&(a.domain=a.domain.filter(Boolean));try{!function(e,t){"undefined"!=typeof window&&void 0!==window.document&&(document.addEventListener("error",(function(t){t&&t.target instanceof Element&&e(t)}),!0),document.addEventListener("load",(function(e){e&&e.target instanceof Element&&t(e)}),!0))}((function(e){try{i(a,e)}catch(e){console.error("retry error captured",e)}}),(function(e){try{!function(e,n){var i=r(e,n);if(!1!==i){var a=i.target,o=i.tagName,s=i.url,c=t(s,e.domain),m=Number(a.dataset.rsbuildRetryTimes)||0;if(0!==m&&"function"==typeof e.onSuccess){var l={times:m,domain:c,url:s,tagName:o};e.onSuccess(l)}}}(a,e)}catch(e){console.error("load error captured",e)}}))}catch(e){console.error("monitor error captured",e)}}({type:["script","link"],domain:[],max:3,test:"",crossOrigin:!0})}()</script></body></html>
<!DOCTYPE html>
<html>
<head>
    <title>李嘉诚家族关系与商业版图</title>
    <meta charset="utf-8">
    <style>
        body { margin: 20px; font-family: "思源黑体", sans-serif; background: #f5f7fa; }
        .container { max-width: 1600px; margin: 0 auto; } /* 调整容器宽度适应双栏 */
        #chart { width: 70%; height: 85vh; border-radius: 12px; background: white; box-shadow: 0 4px 12px rgba(0,0,0,0.08); } /* 调整图表宽度占比 */
        #profile { 
            width: 30%; 
            min-width: 320px; 
            background: white; 
            border-radius: 12px; 
            padding: 24px; 
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }
        #profile h3 { 
            color: #1890ff; 
            margin-top: 0; 
            border-bottom: 2px solid #e6f4ff; 
            padding-bottom: 8px;
        }
        #profile p { line-height: 1.8; margin: 12px 0; }
        .node { cursor: pointer; transition: all 0.3s; }
        .node:hover circle { transform: scale(1.1); }
        .node circle { 
            fill: #e6f4ff; 
            stroke: #1890ff; 
            stroke-width: 2px;
            filter: drop-shadow(0 2px 4px rgba(24, 144, 255, 0.2));
        }
        .node.family circle { stroke: #1890ff; } /* 家人（蓝色） */
        .node.partner circle { stroke: #52c41a; } /* 合作伙伴（绿色） */
        .node.superior circle { stroke: #fa8c16; } /* 上司（橙色） */
        .node text { font: 14px "思源黑体"; fill: #262626; font-weight: 500; }
        .link { stroke: #d9d9d9; stroke-width: 1.5px; stroke-dasharray: 2 2; }
        .link-text { font: 12px "思源黑体"; fill: #595959; }
        .tooltip {
            position: absolute;
            background: white;
            color: #262626;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            pointer-events: none;
            display: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-width: 280px;
            line-height: 1.6;
        }
        .tooltip strong { color: #1890ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>李嘉诚家族关系与商业版图（截至2024年公开信息）</h1>
        <div style="display: flex; gap: 20px;">
            <div id="profile">
                <h3>人物履历</h3>
                <p>点击家族成员查看详细信息</p>
            </div>
            <div id="chart"></div>
        </div>
    </div>

    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script>
        // 完整家族数据（整合所有节点和链接）
        const familyData = {
            nodes: [
                { 
                    id: "li_ka_shing", 
                    name: "李嘉诚", 
                    birth: "1928年7月29日", 
                    role: "商业巨擘/慈善家",
                    assets: "约390亿美元（2024福布斯全球富豪榜）",
                    companies: ["长江和记实业（CK Hutchison）", "长江实业集团（CK Asset）", "赫斯基能源（Husky Energy）"]
                },
                { 
                    id: "chuang_moon_ming", 
                    name: "庄月明", 
                    birth: "1932年-1990年", 
                    role: "配偶（表妹）",
                    note: "香港富商庄静庵之女，曾协助李嘉诚创业"
                },
                { 
                    id: "li_tsz_ku", 
                    name: "李泽钜", 
                    birth: "1964年8月1日", 
                    role: "长江实业集团主席",
                    assets: "约30亿美元（家族信托主要受益人）",
                    companies: ["长江实业集团", "和记黄埔", "加拿大帝国商业银行（CIBC）"]
                },
                { 
                    id: "li_tsz_kei", 
                    name: "李泽楷", 
                    birth: "1966年11月8日", 
                    role: "电讯盈科主席",
                    assets: "约60亿美元",
                    companies: ["电讯盈科（PCCW）", "富卫集团（FWD）", "盈大地产"]
                },
                { 
                    id: "vivian_li", 
                    name: "王俪桥", 
                    birth: "1969年", 
                    role: "妻子",
                    note: "加拿大籍华人，曾任职花旗银行"
                },
                { 
                    id: "li_diankun", 
                    name: "李殿坤", 
                    role: "祖父",
                    note: "清末民初潮州著名商人，经营潮安宏安茶行",
                    links: ["https://zh.wikipedia.org/wiki/%E6%9D%8E%E5%98%89%E8%AF%9A"]
                },
                { 
                    id: "lee_shau_kee", 
                    name: "李兆基", 
                    role: "合作伙伴",
                    assets: "约300亿美元（2024福布斯）",
                    companies: ["恒基兆业地产"],
                    note: "香港四大地产商之一，与李嘉诚长期合作基建项目",
                    links: ["https://www.hendersonland.com"]
                },
                { 
                    id: "chuang_ching_yam", 
                    name: "庄静庵", 
                    role: "前上司/舅父",
                    note: "香港中南钟表有限公司创始人，李嘉诚15岁时的雇主（庄月明父亲）",
                    links: ["http://www.chuangwatch.com"]
                }
            ],
            links: [
                { source: "li_ka_shing", target: "chuang_moon_ming", relation: "配偶" },
                { source: "li_ka_shing", target: "li_tsz_ku", relation: "长子" },
                { source: "li_ka_shing", target: "li_tsz_kei", relation: "次子" },
                { source: "li_tsz_ku", target: "vivian_li", relation: "配偶" },
                { source: "li_ka_shing", target: "li_diankun", relation: "祖父" },
                { source: "li_ka_shing", target: "lee_shau_kee", relation: "合作伙伴" },
                { source: "li_ka_shing", target: "chuang_ching_yam", relation: "前上司" }
            ]
        };

        // 初始化图表容器尺寸
        const chartContainer = document.getElementById('chart');
        const width = chartContainer.offsetWidth;
        const height = chartContainer.offsetHeight;
        const svg = d3.select("#chart")
            .append("svg")
            .attr("width", width)
            .attr("height", height)
            .attr("viewBox", `0 0 ${width} ${height}`);

        // 初始化力导向模拟
        const simulation = d3.forceSimulation(familyData.nodes)
            .force("link", d3.forceLink(familyData.links)
                .id(d => d.id)
                .distance(d => d.relation === "配偶" ? 120 : 180)
            )
            .force("charge", d3.forceManyBody().strength(-280))
            .force("center", d3.forceCenter(width / 2, height / 2));

        // 绘制边
        const link = svg.selectAll("line")
            .data(familyData.links)
            .enter()
            .append("line")
            .attr("class", "link");

        // 绘制边标签
        const linkText = svg.selectAll(".link-text")
            .data(familyData.links)
            .enter()
            .append("text")
            .attr("class", "link-text")
            .text(d => d.relation)
            .style("font-size", "14px")
            .style("fill", "#595959")
            .attr("dy", "1em");

        // 绘制节点（修正script标签嵌套问题）
        const node = svg.selectAll(".node")
            .data(familyData.nodes)
            .enter()
            .append("g")
            .attr("class", d => { 
                if(d.role.includes("配偶") || d.role.includes("祖父") || d.role.includes("妻子")) return "node family";
                if(d.role.includes("合作伙伴")) return "node partner";
                if(d.role.includes("上司")) return "node superior";
                return "node";
            })
            .on("click", handleNodeClick)
            .call(d3.drag()
                .on("start", dragstarted)
                .on("drag", dragged)
                .on("end", dragended));

        node.append("circle")
            .attr("r", 24);

        node.append("text")
            .attr("dy", ".3em")
            .style("text-anchor", "middle")
            .text(d => d.name);

        // 模拟更新回调
        simulation.on("tick", () => {
            link
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);

            linkText
                .attr("x", d => (d.source.x + d.target.x) / 2)
                .attr("y", d => (d.source.y + d.target.y) / 2 - 5);

            node
                .attr("transform", d => `translate(${d.x}, ${d.y})`);
        });

        // 点击事件处理
        function handleNodeClick(event, d) {
            const profileEl = document.getElementById('profile');
            let html = `<h3>${d.name} 详细信息</h3>`;
            
            if(d.birth) html += `<p><strong>出生</strong>: ${d.birth}</p>`;
            if(d.role) html += `<p><strong>身份</strong>: ${d.role}</p>`;
            if(d.assets) html += `<p><strong>资产</strong>: ${d.assets}</p>`;
            if(d.companies) html += `<p><strong>关联公司</strong>: ${d.companies.join("、")}</p>`;
            if(d.note) html += `<p><strong>备注</strong>: ${d.note}</p>`;
        
            if(d.links) {
                html += `<p><strong>参考链接</strong>:<br>`;
                d.links.forEach(link => {
                    html += `<a href="${link}" target="_blank">${link}</a><br>`;
                });
                html += `</p>`;
            }
        
            profileEl.innerHTML = html;
        }

        // 拖拽事件处理
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }
    </script>
</body>
</html>
<script>
    // 扩展家族数据（新增合作伙伴/祖父/上司等角色）
    const familyData = {
        nodes: [
            { 
                id: "li_ka_shing", 
                name: "李嘉诚", 
                birth: "1928年7月29日", 
                role: "商业巨擘/慈善家",
                assets: "约390亿美元（2024福布斯全球富豪榜）",
                companies: ["长江和记实业（CK Hutchison）", "长江实业集团（CK Asset）", "赫斯基能源（Husky Energy）"]
            },
            { 
                id: "chuang_moon_ming", 
                name: "庄月明", 
                birth: "1932年-1990年", 
                role: "李嘉诚已故配偶（表妹）",
                note: "香港富商庄静庵之女，曾协助李嘉诚创业"
            },
            { 
                id: "li_tsz_ku", 
                name: "李泽钜", 
                birth: "1964年8月1日", 
                role: "长江实业集团主席",
                assets: "约30亿美元（家族信托主要受益人）",
                companies: ["长江实业集团", "和记黄埔", "加拿大帝国商业银行（CIBC）"]
            },
            { 
                id: "li_tsz_kei", 
                name: "李泽楷", 
                birth: "1966年11月8日", 
                role: "电讯盈科主席",
                assets: "约60亿美元",
                companies: ["电讯盈科（PCCW）", "富卫集团（FWD）", "盈大地产"]
            },
            { 
                id: "vivian_li", 
                name: "王俪桥", 
                birth: "1969年", 
                role: "李泽钜妻子",
                note: "加拿大籍华人，曾任职花旗银行"
            },
            { 
                id: "li_diankun", 
                name: "李殿坤", 
                role: "李嘉诚祖父",
                note: "清末民初潮州著名商人，经营潮安宏安茶行",
                links: ["https://zh.wikipedia.org/wiki/%E6%9D%8E%E5%98%89%E8%AF%9A"]
            },
            { 
                id: "lee_shau_kee", 
                name: "李兆基", 
                role: "生意合作伙伴",
                assets: "约300亿美元（2024福布斯）",
                companies: ["恒基兆业地产"],
                note: "香港四大地产商之一，与李嘉诚长期合作基建项目",
                links: ["https://www.hendersonland.com"]
            },
            { 
                id: "chuang_ching_yam", 
                name: "庄静庵", 
                role: "前上司/舅父",
                note: "香港中南钟表有限公司创始人，李嘉诚15岁时的雇主（庄月明父亲）",
                links: ["http://www.chuangwatch.com"]
            }
        ],
        links: [
            { source: "li_ka_shing", target: "li_diankun", relation: "祖父" },
            { source: "li_ka_shing", target: "lee_shau_kee", relation: "合作伙伴" },
            { source: "li_ka_shing", target: "chuang_ching_yam", relation: "前上司" }
        ]
    };
</script>
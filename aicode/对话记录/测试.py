with result AS 
    (SELECT customer_id,
         result_id,
         call_status,
         username,
         phone_number,
         company_id,
         batch_id,
         answer_duration,
         robot_answer_duration,
         transfer_time,
         call_start_time,
         answer_time,
         human_answer_duration,
         dm_session_id,
         dm_version,
         call_task_id
    FROM outbound_call_result AS a
    WHERE company_id = 2199
            AND date(call_start_time) >= current_date-20), dm_label AS 
    (SELECT *
    FROM data_center.zx_deal_with_result
    WHERE company_id = 2199
            AND deal_time::timestamp > current_date-20), deal_with_result AS 
    (SELECT deal_no AS order_id,
         customer_id,
         deal_time AS breakpoint_time,
         fee_version,
         premium_total AS price,
         result_id,
         annul_time
    FROM data_center.deal_with_result
    WHERE company_id = 2199), deal_1 AS 
    (SELECT *
    FROM 
        (SELECT customer_id,
         deal_time work_time,
         '支付' AS type, result_id, row_number()
            OVER ( partition by customer_id,deal_time
        ORDER BY  deal_time DESC ) AS num
        FROM data_center.deal_with_result
        WHERE company_id = 2199
                AND deal_time > current_date-20
                AND result_id is NOT null) AS a
        WHERE a.num = 1), wait_pay AS 
        (SELECT *
        FROM 
            (SELECT customer_id,
         order_time work_time,
         '待支付' AS type, result_id, row_number()
                OVER ( partition by customer_id,order_time
            ORDER BY  order_time DESC ) AS num
            FROM data_center.wait_pay_with_result_insurance
            WHERE company_id = 2199
                    AND order_time > current_date-20
                    AND result_id is NOT null) AS a
            WHERE a.num = 1), shuru AS 
            (SELECT *
            FROM 
                (SELECT customer_id,
         order_time work_time,
         '输入激活码' AS type, result_id, row_number()
                    OVER ( partition by customer_id,order_time
                ORDER BY  order_time DESC ) AS num
                FROM data_center.activation_code_with_result_insurance
                WHERE company_id = 2199
                        AND order_time > current_date-20
                        AND type IN (1,9,10)
                        AND result_id is NOT null) AS a
                WHERE a.num = 1), detail_temp AS 
                (SELECT *
                FROM wait_pay
                UNION
                allSELECT *
                FROM shuru), detail_3 AS 
                (SELECT *
                FROM 
                    (SELECT *,
         row_number()
                        OVER (partition by customer_id
                    ORDER BY  work_time DESC ) AS num_1
                    FROM detail_temp) AS a
                    WHERE a.num_1 = 1), detail_2 AS 
                    (SELECT customer_id,
         work_time,
         type,
         result_id
                    FROM deal_1
                    UNION
                    SELECT customer_id,
         work_time,
         type,
         result_id
                    FROM detail_3), detail AS 
                    (SELECT result.call_start_time,
         result.customer_id,
         result.result_id,
         result.answer_time,
         result.batch_id,
         result.dm_version,
         result.call_task_id,
         dm_label.order_id AS zx_order_id,
         result.transfer_time,
         result.human_answer_duration,
         deal_with_result.order_id,
         deal_with_result.customer_id AS order_customer_id,
         deal_with_result.breakpoint_time,
         deal_with_result.price,
         deal_with_result.fee_version,
         detail_2.customer_id AS shuru_customer_id,
         detail_2.type
                    FROM result
                    LEFT JOIN deal_with_result
                        ON result.result_id = deal_with_result.result_id
                    LEFT JOIN dm_label
                        ON result.dm_session_id = dm_label.dm_session_id
                    LEFT JOIN detail_2
                        ON result.result_id = detail_2.result_id), detail_1 AS 
                    (SELECT date(call_start_time) work_date,
         batch_id,
         call_task_id,
         dm_version,
         count(distinct customer_id) call_case_count,
         count(distinct
                        CASE
                        WHEN answer_time is NOT NULL THEN
                        customer_id end) answer_case_count, count(distinct
                        CASE
                        WHEN transfer_time is NOT null
                            AND human_answer_duration
                        BETWEEN 0
                            AND 10 THEN
                        customer_id end) zhuanrenmiaogualiang, count(distinct zx_order_id) agree_case_count, count(distinct
                        CASE
                        WHEN transfer_time is NOT NULL THEN
                        customer_id end) transfer_case_count, count(distinct
                        CASE
                        WHEN transfer_time is NOT null
                            AND human_answer_duration > 0 THEN
                        customer_id end) transfer_success_case_count, coalesce(count(distinct shuru_customer_id), 0) shuru_case_count, coalesce(count(distinct
                        CASE
                        WHEN type = '待支付'
                            OR type = '支付' THEN
                        shuru_customer_id end), 0) tijiao_case_count, coalesce(count(distinct
                        CASE
                        WHEN type = '支付' THEN
                        shuru_customer_id end), 0) pay_case_count, count(distinct order_id) pay_count, count(distinct
                        CASE
                        WHEN fee_version = '2' THEN
                        order_id end) up_count, count(distinct
                        CASE
                        WHEN fee_version = '2' THEN
                        order_customer_id end) up_case_count
                    FROM detail
                    GROUP BY  date(call_start_time), batch_id, dm_version, call_task_id)
                SELECT work_date 外呼日期,
         batch_id 批次,
         call_task_id 计划id,
         dm_version 机器人版本,
         call_case_count 外呼名单量,
         answer_case_count 接通名单量,
         agree_case_count 同意领取名单量,
         transfer_case_count 转人名单量,
         transfer_success_case_count 转人成功用户量,
         zhuanrenmiaogualiang 转人成功秒挂用户量,
         tijiao_case_count 提交名单量,
         pay_case_count 支付名单量,
         up_case_count 升级名单量,
         NULL 同意关注公众号,
         shuru_case_count 输入激活码用户量,
         tijiao_case_count 提交用户量,
         pay_case_count 支付用户量,
         pay_count 支付量,
         up_count 升级量,
         round(1.0 * transfer_success_case_count / NULLIF(answer_case_count,
         0),
         4) \"转人率(转人成功名单量/接通名单量)\",
         round(1.0 * agree_case_count / NULLIF(answer_case_count,
         0),
         4) \"同意领取率(同意领取名单量/接通名单量)\",
         round(1.0 * tijiao_case_count / NULLIF(transfer_success_case_count,
         0),
         4) \"提交率(提交用户量/转人成功用户量)\",
         round(1.0 * up_count / NULLIF(pay_case_count,
         0),
         4) \"升级率(升级量/支付名单量)\"
            FROM detail_1
        ORDER BY  work_date;
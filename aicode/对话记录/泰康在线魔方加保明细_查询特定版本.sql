-- 400 黑牛 AI （泰康魔方/人保魔方) + 利宝重疾 实时转加保 -- 整体统计
WITH result AS (
    SELECT *
    FROM outbound_call_result
    WHERE company_id = 2199
        AND call_task_id IN (2270)
        AND call_status IN ('normalConnection', 'transferFail')
        -- 修改为实际的日期时间值
        AND answer_time BETWEEN '2025-06-06 10:00:00' AND '2025-06-06 14:59:59'
),
COLE AS ( 
    -- 移除未使用的AGE字段，减少数据传输量
    SELECT CUSTOMER_ID 
    FROM OUTBOUND_CALL_CASE 
    WHERE CREATE_TIME > CURRENT_DATE - 2
), 
PRODUCT_LIST AS ( 
    SELECT CODE AS code_temp, PRODUCT, PRODUCT_TYPE, SPU 
    FROM heiniu.heiniu_product_with_code 
    WHERE COMPANY_ID = 2199
),
deal AS (
    SELECT 
        a.*, 
        PRODUCT_LIST.PRODUCT_TYPE, 
        PRODUCT_LIST.SPU
    FROM lingxi.ffx_policy AS a 
    -- 提前过滤policy_status和code，减少JOIN数据量
    LEFT JOIN PRODUCT_LIST 
        ON a.CODE = PRODUCT_LIST.code_temp
    WHERE 
        a.create_time >= CURRENT_DATE - 1 
        AND a.create_time < CURRENT_DATE + 1  -- 简化时间范围条件
        AND policy_status != -3
        AND code NOT IN ('rbbwyl_exclusive', 'rbbwyl_exclusive2')
)
SELECT 
    DATE(result.call_start_time) AS 外呼日期,
    result.dm_version,
    COUNT(DISTINCT result.result_id) AS 接通量,
    COUNT(DISTINCT 
        CASE 
            WHEN product_TYPE = 1
                AND upgrade_price IS NOT NULL 
                AND pay_time IS NOT NULL
            THEN deal.policy_id 
        END
    ) AS 魔方升级量,
    COUNT(DISTINCT 
        CASE 
            WHEN product_TYPE = 1
                AND upgrade_price IS NOT NULL 
                AND (
                    deal.pay_time < result.transfer_time 
                    OR (result.transfer_time IS NULL AND deal.pay_time < result.call_end_time)
                )
                AND pay_time IS NOT NULL
            THEN deal.policy_id 
        END
    ) AS 机席魔方升级量,
    COUNT(DISTINCT 
        CASE 
            WHEN product_TYPE = 1
                AND upgrade_price IS NOT NULL 
                AND pay_time IS NOT NULL
                AND deal.pay_time < result.transfer_time 
                AND transfer_time IS NOT NULL 
                AND result.human_answer_duration IS NOT NULL 
            THEN deal.policy_id 
        END
    ) AS 机席魔方升级转人成功量,
    COUNT(DISTINCT CASE WHEN PRODUCT_TYPE = 2 AND SPU = '重疾' THEN deal.CUSTOMER_ID END) AS 重疾提交用户量,
    COUNT(DISTINCT 
        CASE 
            WHEN PRODUCT_TYPE = 2 AND SPU = '重疾' AND PAY_TIME IS NOT NULL
            THEN deal.CUSTOMER_ID 
        END
    ) AS 重疾成交用户量,
    COUNT(DISTINCT 
        CASE 
            WHEN PRODUCT_TYPE = 2 AND SPU = '重疾' AND PAY_TIME IS NOT NULL
            THEN deal.policy_id 
        END
    ) AS 重疾成交量,
    COUNT(
        CASE 
            WHEN PRODUCT_TYPE = 2 AND SPU = '重疾' AND PAY_TIME IS NOT NULL
            THEN deal.policy_id 
        END
    ) AS 重疾成交量非去重,
    COALESCE(
        SUM(
            CASE 
                WHEN PRODUCT_TYPE = 2 AND SPU = '重疾' AND PAY_TIME IS NOT NULL AND PAY_TYPE = 'year' THEN premium::NUMERIC 
                WHEN PRODUCT_TYPE = 2 AND SPU = '重疾' AND PAY_TIME IS NOT NULL AND PAY_TYPE = 'payAllYear' THEN 
                    CASE WHEN month_to_year_price::NUMERIC > 0 THEN month_to_year_price::NUMERIC ELSE price::NUMERIC + premium::NUMERIC END 
                WHEN PRODUCT_TYPE = 2 AND SPU = '重疾' AND PAY_TIME IS NOT NULL AND PAY_TYPE = 'month' THEN price::NUMERIC * 11 + premium::NUMERIC 
            END
        ), 0
    )::NUMERIC(10,2) AS 重疾成交保费
FROM result
LEFT JOIN deal ON result.customer_id = deal.customer_id::VARCHAR
LEFT JOIN COLE ON result.CUSTOMER_ID = COLE.CUSTOMER_ID
GROUP BY DATE(result.call_start_time), result.dm_version
ORDER BY DATE(result.call_start_time), result.dm_version;
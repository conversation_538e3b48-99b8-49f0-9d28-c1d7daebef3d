-- ========================================================================
-- 400 黑牛 AI 流程流转情况统计查询
-- 业务：泰康魔方/人保魔方 + 利宝重疾 实时转加保整体统计
-- 日期：2025-06-26 外呼数据及相关成交情况
-- ========================================================================

WITH
-- 1. 外呼结果数据
result AS (
    SELECT * FROM outbound_call_result
    WHERE company_id = 2199
        AND call_task_id IN (2137, 2136)
        AND call_status IN ('normalConnection', 'transferFail')
        AND answer_time BETWEEN '2025-06-26 00:00:00' AND '2025-06-26 23:50:59'
),

-- 2. 客户基础信息
COLE AS (
    SELECT CUSTOMER_ID, AGE FROM OUTBOUND_CALL_CASE
    WHERE CREATE_TIME > CURRENT_DATE - 5
),

-- 3. 产品信息配置
PRODUCT_LIST AS (
    SELECT CODE AS code_temp, PRODUCT, PRODUCT_TYPE, SPU
    FROM heiniu.heiniu_product_with_code
    WHERE COMPANY_ID = 2199
),

-- 4. 页面输入数据（activation_code提取）
shuru AS (
    SELECT CAST(customer_id AS VARCHAR) AS customer_id,
           activation_code,
           create_time AS shuru_time
    FROM (
        SELECT customer_id, activation_code, create_time,
               row_number() OVER (PARTITION BY customer_id ORDER BY create_time DESC) AS rn
        FROM lingxi.plan_record_info_new
        WHERE type IN (9, 16)
    ) ranked
    WHERE rn = 1
),

-- 5. 成交数据融合（核心业务数据）
deal AS (
    SELECT a.*, PRODUCT_LIST.PRODUCT_TYPE, PRODUCT_LIST.SPU,
           shuru.activation_code, shuru.shuru_time
    FROM lingxi.ffx_policy AS a
        LEFT JOIN PRODUCT_LIST ON a.CODE = PRODUCT_LIST.code_temp
        LEFT JOIN shuru ON CAST(a.customer_id AS VARCHAR) = shuru.customer_id
    WHERE a.create_time BETWEEN CURRENT_DATE - 5 AND CURRENT_DATE + 1
        AND a.policy_status != -3
        AND a.code NOT IN ('rbbwyl_exclusive', 'rbbwyl_exclusive2')
)
-- ============================================================================
-- 6. 主查询：统计分析结果
-- ============================================================================
SELECT
    -- ========================================================================
    -- 基础维度字段
    -- ========================================================================
    DATE(result.call_start_time) AS 外呼日期,
    result.dm_version AS 模型版本,

    -- ========================================================================
    -- 基础统计指标
    -- ========================================================================
    COUNT(DISTINCT result.result_id) AS 接通量,
    COUNT(DISTINCT
        CASE
            WHEN deal.activation_code IS NOT NULL
            THEN deal.customer_id
        END
    ) AS 页面量,

    -- ========================================================================
    -- 魔方产品相关指标（PRODUCT_TYPE = 1）
    -- ========================================================================
    COUNT(DISTINCT
        CASE
            WHEN deal.PRODUCT_TYPE = 1
                AND deal.upgrade_price IS NOT NULL
                AND deal.pay_time IS NOT NULL
            THEN deal.policy_id
        END
    ) AS 魔方升级量,

    COUNT(DISTINCT
        CASE
            WHEN deal.PRODUCT_TYPE = 1
                AND deal.upgrade_price IS NOT NULL
                AND deal.pay_time IS NOT NULL
                AND (
                    deal.pay_time < result.transfer_time
                    OR (result.transfer_time IS NULL AND deal.pay_time < result.call_end_time)
                )
            THEN deal.policy_id
        END
    ) AS 机席魔方升级量,

    COUNT(DISTINCT
        CASE
            WHEN deal.PRODUCT_TYPE = 1
                AND deal.upgrade_price IS NOT NULL
                AND deal.pay_time IS NOT NULL
                AND deal.pay_time < result.transfer_time
                AND result.transfer_time IS NOT NULL
                AND result.human_answer_duration IS NOT NULL
            THEN deal.policy_id
        END
    ) AS 机席魔方升级转人成功量,

    -- ========================================================================
    -- 重疾产品相关指标（PRODUCT_TYPE = 2, SPU = '重疾'）
    -- ========================================================================
    COUNT(DISTINCT
        CASE
            WHEN deal.PRODUCT_TYPE = 2
                AND deal.SPU = '重疾'
            THEN deal.customer_id
        END
    ) AS 重疾提交用户量,

    COUNT(DISTINCT
        CASE
            WHEN deal.PRODUCT_TYPE = 2
                AND deal.SPU = '重疾'
                AND deal.PAY_TIME IS NOT NULL
            THEN deal.customer_id
        END
    ) AS 重疾成交用户量,

    COUNT(DISTINCT
        CASE
            WHEN deal.PRODUCT_TYPE = 2
                AND deal.SPU = '重疾'
                AND deal.PAY_TIME IS NOT NULL
            THEN deal.policy_id
        END
    ) AS 重疾成交量,

    COUNT(DISTINCT
        CASE
            WHEN deal.PRODUCT_TYPE = 2
                AND deal.SPU = '重疾'
                AND deal.PAY_TIME IS NOT NULL
                AND deal.surrender_time IS NOT NULL
                AND DATE(deal.surrender_time) = DATE(deal.PAY_TIME)
            THEN deal.policy_id
        END
    ) AS 当日重疾退保量,

    COUNT(
        CASE
            WHEN deal.PRODUCT_TYPE = 2
                AND deal.SPU = '重疾'
                AND deal.PAY_TIME IS NOT NULL
            THEN deal.policy_id
        END
    ) AS 重疾成交量非去重,

    -- ========================================================================
    -- 重疾成交保费计算（按支付类型分类计算）
    -- ========================================================================
    COALESCE(
        SUM(
            CASE
                -- 年付保费
                WHEN deal.PRODUCT_TYPE = 2
                    AND deal.SPU = '重疾'
                    AND deal.PAY_TIME IS NOT NULL
                    AND deal.PAY_TYPE = 'year'
                THEN CAST(deal.premium AS DECIMAL(10,2))

                -- 趸交保费
                WHEN deal.PRODUCT_TYPE = 2
                    AND deal.SPU = '重疾'
                    AND deal.PAY_TIME IS NOT NULL
                    AND deal.PAY_TYPE = 'payAllYear'
                THEN CASE
                    WHEN CAST(deal.month_to_year_price AS DECIMAL(10,2)) > 0
                    THEN CAST(deal.month_to_year_price AS DECIMAL(10,2))
                    ELSE CAST(deal.price AS DECIMAL(10,2)) + CAST(deal.premium AS DECIMAL(10,2))
                END

                -- 月付保费（转换为年保费）
                WHEN deal.PRODUCT_TYPE = 2
                    AND deal.SPU = '重疾'
                    AND deal.PAY_TIME IS NOT NULL
                    AND deal.PAY_TYPE = 'month'
                THEN CAST(deal.price AS DECIMAL(10,2)) * 11 + CAST(deal.premium AS DECIMAL(10,2))
            END
        ), 0
    ) AS 重疾成交保费

-- ============================================================================
-- 7. 数据关联和分组
-- ============================================================================
FROM result
    LEFT JOIN deal
        ON result.customer_id = CAST(deal.customer_id AS VARCHAR)
    LEFT JOIN COLE
        ON result.CUSTOMER_ID = COLE.CUSTOMER_ID

GROUP BY
    DATE(result.call_start_time),
    result.dm_version

ORDER BY
    DATE(result.call_start_time),
    result.dm_version;

WITH filtered_sessions AS (
    SELECT DISTINCT ocr.dm_session_id AS session_id
    FROM outbound_call_result ocr
    WHERE ocr.company_id = 2257
    AND ocr.answer_time BETWEEN '2025-04-09 00:00:00' AND '2025-04-09 23:00:00'
    AND ocr.dm_version IN ('大模型泰康直拨公众号新农合-成交转-T-0407', '大模型泰康直拨公众号新农合-成交转-0402')
    AND ocr.call_status != 'dmTimeOut'
    AND ocr.workspace_id IN (368, 341)
),
search_box_sessions AS (
    SELECT DISTINCT dr.session_id
    FROM dm_record dr
    JOIN filtered_sessions fs ON dr.session_id = fs.session_id
    WHERE dr.workspace_id IN (368, 341)
    AND dr.msg_time >= '2025-04-09 00:00:00' AND dr.msg_time <= '2025-04-09 23:00:00'
    AND dr.content_label::jsonb ? '流程节点:搜索框'
),
excluded_sessions AS (
    SELECT DISTINCT session_id
    FROM dm_record
    WHERE content_label::jsonb ? '流程节点:1212121'
)
SELECT dr.session_id,
       dr.msg_time,
       dr.speaker_type,
       dr.msg_content
FROM dm_record dr
JOIN search_box_sessions sbs ON dr.session_id = sbs.session_id
WHERE dr.workspace_id IN (368, 341)
AND dr.msg_time >= '2025-04-09 00:00:00' AND dr.msg_time <= '2025-04-09 23:00:00'
AND NOT EXISTS (
    SELECT 1 FROM excluded_sessions es WHERE es.session_id = dr.session_id
)
ORDER BY dr.session_id, dr.msg_time, dr.speaker_type DESC;

with result as (select result_id,
                       case_id,
                       customer_id,
                       call_start_time,
                       answer_time,
                       exe_id,
                       dm_version,
                       call_task_id,
                       username,
                       transfer_time,
                       transfer_success_time,
                       call_status,
                       robot_answer_duration,
                       human_answer_duration,
                       answer_duration,
                       full_name,
                       json_data::json ->> 'type' as type
                from screen.public.outbound_call_result
                where company_id = 2257
                  and outbound_call_result.answer_time >= current_date - 10),
     qw_ww_friend as (
         select *
         from ql_work_wechat.qw_ww_friend
     ),
     repaid_detail as (select *
                       from (select *,
                                    json_data::json ->> 'paidPeriods'                                           缴费期数,
                                    json_data::json ->> 'nextPaymentDate'                                       下次缴费时间,
                                    row_number()
                                    over (partition by order_id order by breakpoint_time desc,create_time desc) num
                             from breakpoint_data
                             where company_id = 2257
                               and json_data::json ->> 'source' = '订单'
                               and stage = '续期'
                               and create_time > '2024-06-16') break_base
                       where num = 1),
     repaid_detail_follow_external as (
         select policy_no                                                        order_id,
                pay_sequence                                                     缴费期数,
                to_char(to_date(left(pay_time, 8), 'yyyymmdd'), 'yyyy-mm-dd') as 下次缴费时间,
                if(amount is not null, amount::numeric / 100, 0::numeric)        premium_follow
         from (
                  select *,
                         row_number()
                         over (partition by policy_no order by pay_time desc,create_time desc) num
                  from ins.payment_info
                  where pay_status = '3'
              ) break_base
         where num = 1
     ),
     call_case as (select sex, age, customer_id, full_name
                   from screen.public.outbound_call_case
                   where company_id = 2257),
     temp_call_task as (select id, name as task_name from call_task where company_id = 2257),
     temp_imp as (select id, substring(name from '[0-9]拨') exeName from call_task_implement where company_id = 2257),
     temp_user as (select username, real_name, workplace_id from p_user),
     temp_workplace as (select id, name, name_abbreviation from p_workplace),
     result_with_deal as (select customer_id,
                                 result_id,
                                 annul_time,
                                 json_data::json ->> 'paytype'     as paytype,
                                 json_data::json ->> 'payway'      as payway,
                                 quit_time,
                                 premium_follow                    as premium_follow,
                                 json_data::json ->> 'sign_status' as sign_status,
                                 premium_total                     as nhPremium,
                                 deal_no                           as order_id,
                                 deal_time                         as breakpoint_time,
                                 json_data::json ->> 'productCode' as productCode,
                                 json_data,
                                 product_name
                          from data_center.deal_with_result
                          where company_id = 2257
                            and deal_with_result.deal_time >= date_trunc('month', CURRENT_DATE)
     ),
     detail as (select result.*,
                       result_with_deal.customer_id                                         deal_customer_id,
                       age,
                       sex,
                       paytype,
                       payway,
                       result_with_deal.order_id,
                       result_with_deal.breakpoint_time,
                       result_with_deal.nhPremium,
                       temp_call_task.task_name,
                       temp_imp.exeName,
                       annul_time,
                       quit_time,
                       temp_user.real_name,
                       sign_status,
                       temp_workplace.name_abbreviation,
                       result_with_deal.product_name,
                       result_with_deal.json_data,
                       coalesce(coalesce(repaid_detail.缴费期数,
                                         repaid_detail_follow_external.缴费期数), '1')          缴费期数,
                       coalesce(repaid_detail.下次缴费时间, repaid_detail_follow_external.下次缴费时间) 下次缴费时间,
                       productCode,
                       result_with_deal.premium_follow                                      下次缴费金额,
                       qw_ww_friend.channel_name                                            加微客户
                from result_with_deal
                         left join call_case on result_with_deal.customer_id = call_case.customer_id
                         left join result on result_with_deal.result_id = result.result_id
                         left join temp_call_task ON result.call_task_id = temp_call_task.id
                         left join temp_imp ON result.exe_id = temp_imp.id
                         LEFT JOIN temp_user ON result.username = temp_user.username and
                                                result.transfer_success_time <= result_with_deal.breakpoint_time
                         LEFT JOIN temp_workplace ON temp_user.workplace_id = temp_workplace.id
                         LEFT join repaid_detail on result_with_deal.order_id = repaid_detail.order_id
                         LEFT join qw_ww_friend on result_with_deal.customer_id = qw_ww_friend.channel_name
                         LEFT join repaid_detail_follow_external
                                   on result_with_deal.order_id = repaid_detail_follow_external.order_id)
select deal_customer_id                                                                客户id,
       case_id                                                                         零犀id,
       2257                                                                            空间id,
       type                                                                            案件type,
       order_id                                                                        保单号,
       date(breakpoint_time)                                                           支付时间,
       product_name                                                                    商品,
       productCode                                                                     产品编码,
       case when quit_time is null then '在保' else '退保' end                             保单状态,
       sign_status                                                                     签约状态,
       annul_time                                                                      解约时间,
       quit_time                                                                       退保时间,
       缴费期数                                                                            实际缴费期数,
       下次缴费金额                                                                          续期缴费金额,
       下次缴费时间                                                                          续期缴费时间,
       case
           when (下次缴费时间 is null or 下次缴费时间 = '') then 0
           when current_date - to_date(下次缴费时间, 'yyyy-mm-dd') < 0 then 0
           else current_date - to_date(下次缴费时间, 'yyyy-mm-dd') end                       距离上次支付天数,
       case
           when paytype = '0' then '已续期'
           when 缴费期数 is null then '未获取到缴费期数'
           when ceil((current_date - date(breakpoint_time))::numeric / 31) - 缴费期数::integer = 0 then '已续期'
           else '未续期' end                                                              是否续期,
       nhPremium::numeric                                                              年化保费,
       case when paytype = '1' then '月缴' when paytype = '0' then '年缴' else paytype end 支付类型,
       payway                                                                          支付方式,
       call_task_id                                                                    计划id,
       task_name                                                                       计划名称,
       real_name                                                                       成单坐席,
       name_abbreviation                                                               职场简称,
       result_id                                                                       话单id,
       date(call_start_time)                                                           外呼日期,
       dm_version                                                                      机器人版本,
       full_name                                                                       用户姓名,
       age                                                                             年龄,
       case when sex = 0 then '女' when sex = 1 then '男' else '未知' end                  性别,
       breakpoint_time                                                                 支付时间点,
       transfer_success_time                                                           转人成功时间,
       加微客户
from detail
where order_id is not null 
order by breakpoint_time desc;

# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2024/1/3 17:55
import asyncio
import time

import aiohttp
from loguru import logger
from collections import Counter
def analyze_results(results):
    # Remove whitespace and normalize responses
    cleaned_results = [r.strip() for r in results]
    # Count occurrences of each response
    counter = Counter(cleaned_results)
    
    # Print statistics
    print("\n=== Results Analysis ===")
    print(f"Total responses: {len(results)}")
    print("\nResponse distribution:")
    for response, count in counter.most_common():
        percentage = (count / len(results)) * 100
        print(f"- {response}: {count} times ({percentage:.1f}%)")
    print("=" * 50)

async def request_llama3(prompt, temperature=0.3):
    headers = {
        "Content-Type": "application/json",
    }
    payload = {
        "model": 'llama3',
        "messages": [{"role": "user",
                      "content": "<|start_header_id|>user<|end_header_id|>\n\n" + prompt.strip() + "<|eot_id|>" + "\n<|start_header_id|>assistant<|end_header_id|>\n\n"}],
        "stream": False,
        "temperature": temperature,
        "max_tokens": 8192,
    }
    start = time.time()
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post('http://**************:8003/v1/chat/completions', headers=headers,
                                    json=payload) as resp:
                response = await resp.json()
                text = response["choices"][0]['message']['content']
        end = time.time()
        logger.info(f'请求耗时：{end - start}s, 内容:{text}')
    except Exception as e:
        logger.error(e)
        # return await request_llama3(prompt)
    return text

async def execute():
    prompt = '''
你是一名专业的保险销售员，你的任务是根据【销售链路】【操作流程】【对话记录】判断下一步的【销售流程节点】。

销售链路：用户已经投保了百万医疗保险，页面跳转到了重疾险页面，给用户介绍重疾险，引导用户投保重疾险并通过银行卡支付保费。

保险产品类型：重疾险
支付方式：银行卡

用户操作流程：
1、页面跳转到重疾险页面，点击[完善重疾保障]，再点击[保费测算]，会出现[立即投保]，然后再点击[立即投保]。
2、点完立即投保按钮后，会出现选择银行卡页面，选择常用的银行卡，无需输入卡号，点击[选择]后，会跳转到验证码页面，点击[获取验证码]，输入短信验证码，点击确认付款

销售流程节点定义：


节点|节点定义
---|---
进入重疾险页面|确认用户现在的页面上是否有重疾险相关内容，让用户点击完善重疾保障按钮，并确认用户看到重疾险产品页面，产品页面有重疾险的产品介绍、有[保费测试]按钮，用户操作完成（看到重疾险产品页面或者页面内其他信息）则进行下一步
介绍体检卡|给用户介绍赠送的体检卡内容与价值，用户了听清了则进行下一步
讲解重疾险|向用户简单介绍重疾险，用户听清了则进行下一步
保费测算|让点击保费测算按钮，并确认用户看到对应费用，用户操作完成（看到费用）则进行下一步
点击立即投保|让用户点击立即投保按钮。用户操作完成则进行下一步
支付费用|引导用户支付费用，用户操作完成则进行下一步
领取权益|用户投保成功后，向用户说明权益领取方式，及可以为家人购买重疾险，用户听清了则进行下一步
挂机|完成销售步骤

销售流程节点流转顺序：
进入重疾险页面->介绍体检卡->讲解重疾险->保费测算->点击立即投保->支付费用->领取权益->挂机

注意：
1、需要用户操作的节点，用户的回应分为：用户没听清、用户拒绝操作、无法判断用户操作状态、用户没操作、用户操作中、用户操作遇到问题、用户操作完成；不需要用户操作的节点，用户的回应分为：用户没听清、用户听清了。节点定义时已经定义了什么情况流转到下一步，根据节点的定义一步一步的分析下一步的销售流程节点。
2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。
3、只能输出销售流程节点中的节点名称。
4、销售流程节点的流转顺序从前往后执行，结合用户的表达，可以进行往回跳转重复引导用户操作或往后跳过用户已经完成的节点。
5、目的是完成销售，只能在完成销售流程时输出挂机。
6、对话开始时可能在解答之前保单的问题，如果发现对话未涉及到流程节点相关对话，从第一个流程节点开始即可。



对话记录如下：
```
保险销售员:(看到重疾险页面)哦，就是一个600百万医疗保险，医社保报销完剩余的合同约定二次报销支付好之后，会看到恭喜您成功投保医疗保障，下面有个完善重疾保障，您点下，点完之后有没有看到54万无忧重疾保障，尊享体检卡免费赠送，看到了吧？
用户:我这个我为啥我整完了还还让我输入啊。
保险销售员:嗯，补充医保总共两部分，都是固定完善的，每个人都要有，刚刚那个是住院报销，这个是门诊确诊即赔的，这个弄完就没了，以后生病赔2笔钱，看病钱赔一次，看病后再报一次，这样讲好理解吧？？？
用户:嗯，
保险销售员:(介绍体检卡)这个体检卡您看一下，它是免费赠送的，全国通用，没有任何费用，你自己用或者是给家人用都行，价值 235 元免费赠送的，一年有效，您什么时候去都可以，好理解吧？
用户:那我这个我这完事了。
保险销售员:(讲解重疾险)就医补助总共两部分，每个人都是一样的，固定完善的，这个是补助门诊确诊即赔的，可以先拿钱后看病，刚刚那个是住院报销的，一前一后就有两笔钱了，医疗费用基本都包含在内，不用动自己家里的积蓄了。我这么说好理解吧？
用户:你看，等会儿，我先我先那啥
保险销售员:嗯，没事，不着急，我等您一下？？？
用户:本人已投保
用户:本人已投保，这时候仅需什么
保险销售员:(保费测算)然后点下下面的保费测算，看下费用是不是几块钱啊，是几块钱吧？
用户:两分钱每天，然后
保险销售员:(点击立即投保)那您也是享受到了这个补贴减免，相当于打了5折，然后您点下右下角的立即投保，看下有没有这个完善资格，您点下，点好了吧？
用户:我现在没有立即投保，只有一个立即领取。
保险销售员:(点击立即投保)这个交完就没有了，总共就两个，所有人都一样是惠民福利，你刚才交了一个了，还有一个，一个是门诊确诊即赔，一个住院报销，您点下那个立即投保，点好了吧？
用户:我没有立即投保了。
保险销售员:就是您先点下下面的保费测算，点完之后点下右下角的立即投保，点好了吧？？？
用户:保费测算
保险销售员:(保费测算)然后点下下面的保费测算，看下费用是不是几块钱啊，是几块钱吧？
用户:你你，你稍等一下。
保险销售员:嗯，不着急（加5s静音）？？？
用户:那领取码是多少了？我调出来了。

```

下一步的销售流程节点：
'''

    # 创建10个请求任务
    tasks = []
    for i in range(10):
        tasks.append(request_llama3(prompt, temperature=0.5))  # 增加温度增加多样性

    # 并行执行所有请求任务
    results = await asyncio.gather(*tasks)

    # 输出所有结果
    for i, result in enumerate(results, 1):
        print(f"\n--- 结果 {i} ---")
        print(result.strip())
        print("-" * 50)


if __name__ == "__main__":
    asyncio.run(execute())

-- 400 黑牛 AI （泰康魔方/人保魔方) + 利宝重疾 实时转加保 -- 整体统计
WITH result AS (
    SELECT *
    FROM outbound_call_result
    WHERE company_id = 2199
        -- and call_task_id not IN (2270)
        AND call_status IN ('normalConnection', 'transferFail')
        -- 修改为实际的日期时间值
        AND answer_time BETWEEN '2024-01-01 00:00:00' AND '2024-01-31 23:59:59'
        AND (
            dm_version LIKE '%成交转%'
            OR dm_version LIKE '%纯机%'
            OR dm_version LIKE '%大模型黑牛泰康直拨加重疾-A公众号-0312%'
        )
        -- and dm_version != '大模型黑牛泰康无三要素直投公众号新农合-成交转-0325'
        -- and dm_version not like '%无三要素%'
        -- and batch_id not like '%AI挂短%'
),
deal AS (
    SELECT a.*
    FROM lingxi.ffx_policy AS a
    WHERE a.pay_time BETWEEN '2024-01-01 00:00:00' AND '2024-01-31 23:59:59'
        -- 成交状态
        AND policy_status != -3
        AND code NOT IN ('rbbwyl_exclusive', 'rbbwyl_exclusive2')
)
SELECT 
    -- 保留外呼日期
    DATE(result.call_start_time) AS 外呼日期,
    result.dm_version,
    COUNT(DISTINCT result.result_id) AS 接通量,
    COUNT(DISTINCT 
        CASE 
            WHEN (code LIKE '%rbbwyl%' OR code LIKE '%zxbwyl-djmf-v%' OR code LIKE '%zxbwyl-djmf-v%') 
                AND upgrade_price IS NOT NULL 
            THEN deal.policy_id 
        END
    ) AS 魔方升级量,
    COUNT(DISTINCT 
        CASE 
            WHEN (code LIKE '%rbbwyl%' OR code LIKE '%zxbwyl-djmf-v%') 
                AND upgrade_price IS NOT NULL 
                AND (
                    deal.pay_time < result.transfer_time 
                    OR (result.transfer_time IS NULL AND deal.pay_time < result.call_end_time)
                )
            THEN deal.policy_id 
        END
    ) AS 机席魔方升级量,
    COUNT(DISTINCT 
        CASE 
            WHEN (code LIKE '%rbbwyl%' OR code LIKE '%zxbwyl-djmf-v%') 
                AND upgrade_price IS NOT NULL 
                AND deal.pay_time < result.transfer_time 
                AND transfer_time IS NOT NULL 
                AND result.human_answer_duration IS NOT NULL 
            THEN deal.policy_id 
        END
    ) AS 机席魔方升级转人成功量,
    COUNT(DISTINCT 
        CASE 
            WHEN code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
            THEN deal.policy_id 
        END
    ) AS 重疾成交量,
    COUNT(
        CASE 
            WHEN code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
            THEN deal.policy_id 
        END
    ) AS 重疾成交量非去重,
    COALESCE(
        SUM(
            CASE 
                WHEN (pay_type = 'year' OR pay_type = 'payAllYear') 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                THEN price::numeric + premium::numeric 
            END
        ), 0
    ) + COALESCE(
        SUM(
            CASE 
                WHEN (month_to_year_pay_status IS NULL OR month_to_year_pay_status != 1) 
                    AND pay_type = 'month' 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                THEN price::numeric * 11 + premium::numeric 
            END
        ), 0
    ) + COALESCE(
        SUM(
            CASE 
                WHEN month_to_year_pay_status = 1 
                    AND pay_type = 'month' 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                THEN month_to_year_price::numeric + premium::numeric 
            END
        ), 0
    ) AS 重疾成交保费,
    COUNT(DISTINCT 
        CASE 
            WHEN code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                AND (pay_type = 'year' OR pay_type = 'payAllYear' OR month_to_year_pay_status = 1) 
            THEN deal.policy_id 
        END
    ) AS 重疾年缴量,
    COALESCE(
        SUM(
            CASE 
                WHEN code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND (pay_type = 'year' OR pay_type = 'payAllYear') 
                THEN price::numeric + premium::numeric 
            END
        ), 0
    ) + COALESCE(
        SUM(
            CASE 
                WHEN code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND (month_to_year_pay_status = 1) 
                THEN month_to_year_price::numeric + premium::numeric 
            END
        ), 0
    ) AS 重疾年缴保费,
    COUNT(DISTINCT 
        CASE 
            WHEN code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                AND result.human_answer_duration IS NOT NULL 
            THEN deal.policy_id 
        END
    ) AS 坐席重疾成交量,
    COUNT(DISTINCT 
        CASE 
            WHEN code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                AND (pay_type = 'year' OR pay_type = 'payAllYear' OR month_to_year_pay_status = 1) 
                AND result.human_answer_duration IS NOT NULL 
            THEN deal.policy_id 
        END
    ) AS 坐席重疾年成交量,
    COALESCE(
        SUM(
            CASE 
                WHEN (pay_type = 'year' OR pay_type = 'payAllYear') 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND result.human_answer_duration IS NOT NULL 
                THEN price::float + premium::float 
            END
        ), 0
    ) + COALESCE(
        SUM(
            CASE 
                WHEN (month_to_year_pay_status IS NULL OR month_to_year_pay_status != 1) 
                    AND pay_type = 'month' 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND result.human_answer_duration IS NOT NULL 
                THEN price::float * 11 + premium::float 
            END
        ), 0
    ) + COALESCE(
        SUM(
            CASE 
                WHEN month_to_year_pay_status = 1 
                    AND pay_type = 'month' 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND result.human_answer_duration IS NOT NULL 
                THEN month_to_year_price::float + premium::float 
            END
        ), 0
    ) AS 坐席重疾保费,
    COALESCE(
        SUM(
            CASE 
                WHEN code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND result.human_answer_duration IS NOT NULL 
                    AND (pay_type = 'year' OR pay_type = 'payAllYear') 
                THEN price::float + premium::float 
            END
        ), 0
    ) + COALESCE(
        SUM(
            CASE 
                WHEN code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND (month_to_year_pay_status = 1) 
                    AND result.human_answer_duration IS NOT NULL 
                THEN month_to_year_price::numeric + premium::numeric 
            END
        ), 0
    ) AS 坐席重疾年缴保费,
    COUNT(DISTINCT 
        CASE 
            WHEN code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                AND username IS NULL 
                AND pay_time < result.call_end_time 
            THEN deal.policy_id 
        END
    ) AS 机重疾成交量,
    COUNT(DISTINCT 
        CASE 
            WHEN code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                AND (pay_type = 'year' OR pay_type = 'payAllYear' OR month_to_year_pay_status = 1) 
                AND username IS NULL 
                AND pay_time < result.call_end_time 
            THEN deal.policy_id 
        END
    ) AS 机重疾年成交量,
    COALESCE(
        SUM(
            CASE 
                WHEN (pay_type = 'year' OR pay_type = 'payAllYear') 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND username IS NULL 
                    AND pay_time < result.call_end_time 
                THEN price::float + premium::float 
            END
        ), 0
    ) + COALESCE(
        SUM(
            CASE 
                WHEN (month_to_year_pay_status IS NULL OR month_to_year_pay_status != 1) 
                    AND pay_type = 'month' 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND username IS NULL 
                    AND pay_time < result.call_end_time 
                THEN price::float * 11 + premium::float 
            END
        ), 0
    ) + COALESCE(
        SUM(
            CASE 
                WHEN month_to_year_pay_status = 1 
                    AND pay_type = 'month' 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND username IS NULL 
                    AND pay_time < result.call_end_time 
                THEN month_to_year_price::float + premium::float 
            END
        ), 0
    ) AS 机重疾成交保费,
    COALESCE(
        SUM(
            CASE 
                WHEN code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND (pay_type = 'year' OR pay_type = 'payAllYear') 
                    AND username IS NULL 
                    AND pay_time < result.call_end_time 
                THEN price::float + premium::float 
            END
        ), 0
    ) + COALESCE(
        SUM(
            CASE 
                WHEN month_to_year_pay_status = 1 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND username IS NULL 
                    AND pay_time < result.call_end_time 
                THEN month_to_year_price::float + premium::float 
            END
        ), 0
    ) AS 机重疾年缴保费,
    COUNT(DISTINCT 
        CASE 
            WHEN code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                AND (policy_status > 1) 
            THEN deal.customer_id 
        END
    ) AS 退保量,
    COUNT(DISTINCT 
        CASE 
            WHEN (pay_type = 'year' OR pay_type = 'payAllYear' OR month_to_year_pay_status = 1) 
                AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                AND (policy_status > 1) 
            THEN deal.customer_id 
        END
    ) AS 年缴退保量,
    COALESCE(
        SUM(
            CASE 
                WHEN (pay_type = 'year' OR pay_type = 'payAllYear') 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND surrender_time IS NOT NULL 
                THEN price::float + premium::float 
            END
        ), 0
    ) + COALESCE(
        SUM(
            CASE 
                WHEN (month_to_year_pay_status IS NULL OR month_to_year_pay_status != 1) 
                    AND pay_type = 'month' 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND surrender_time IS NOT NULL 
                THEN price::float * 11 + premium::float 
            END
        ), 0
    ) + COALESCE(
        SUM(
            CASE 
                WHEN month_to_year_pay_status = 1 
                    AND pay_type = 'month' 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND surrender_time IS NOT NULL 
                THEN month_to_year_price::float + premium::float 
            END
        ), 0
    ) AS 重疾退保保费,
    COALESCE(
        SUM(
            CASE 
                WHEN (pay_type = 'year' OR pay_type = 'payAllYear') 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND surrender_time IS NOT NULL 
                THEN price::float + premium::float 
            END
        ), 0
    ) + COALESCE(
        SUM(
            CASE 
                WHEN month_to_year_pay_status = 1 
                    AND code IN ('tkzj_u', 'lb-wyzj', 'lb-wyzj-pro') 
                    AND surrender_time IS NOT NULL 
                THEN month_to_year_price::float + premium::float 
            END
        ), 0
    ) AS 年缴重疾退保保费,
    COUNT(DISTINCT result.username) AS 上线人数
FROM result
LEFT JOIN deal ON result.customer_id = deal.customer_id::varchar
GROUP BY DATE(result.call_start_time), result.dm_version
ORDER BY DATE(result.call_start_time), result.dm_version;

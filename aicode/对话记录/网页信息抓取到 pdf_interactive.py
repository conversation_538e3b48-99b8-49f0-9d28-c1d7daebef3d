import os
import sys
import time
import asyncio
from datetime import datetime

# 目标 URL
url = 'https://api-b.dcarapi.com/motor/feoffline/usedcar_detect_midas/midas-preview?app_id=motor.sh_inspection.pack_lib&inspection_id=R7493452322866122814&nonce_str=jpByQIjAnlxNOIwB&report_type=2&time_stamp=1745228869&sign=c7e3ad4458e08270884b2293ac11cb4b&page_id=11&share_did=2e4058ce2467b3ae430328aece2637bb&share_uid=8a6949406e03cc318d5e3f4ea881c25e&share_token=96aaabac-3cba-4f14-8bc6-7c6e5e9b6d7b'

# 设置输出目录和文件
output_dir = os.path.dirname(os.path.abspath(__file__))
pdf_path = os.path.join(output_dir, 'vehicle_inspection_report.pdf')
html_path = os.path.join(output_dir, 'webpage_content.html')
debug_dir = os.path.join(output_dir, 'debug_screenshots')

# 创建调试截图目录
if not os.path.exists(debug_dir):
    os.makedirs(debug_dir)

print(f"输出目录: {output_dir}")
print(f"PDF将保存为: {pdf_path}")
print(f"HTML将保存为: {html_path}")
print(f"调试截图目录: {debug_dir}")

# 是否使用交互模式（允许手动操作）
INTERACTIVE_MODE = True
INTERACTIVE_TIMEOUT = 60  # 交互模式下等待用户操作的时间（秒）

def check_dependencies():
    """检查并安装必要的依赖"""
    try:
        # 尝试导入playwright
        import playwright
        print("Playwright库已安装")
    except ImportError:
        print("Playwright库未安装，正在安装...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "playwright"])
            subprocess.check_call([sys.executable, "-m", "playwright", "install", "chromium"])
            print("Playwright安装成功")
        except Exception as e:
            print(f"安装Playwright失败: {e}")
            print("请手动安装Playwright:")
            print("1. pip install playwright")
            print("2. playwright install chromium")
            sys.exit(1)

def take_debug_screenshot(page, name):
    """保存调试截图"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    screenshot_path = os.path.join(debug_dir, f"{timestamp}_{name}.png")
    page.screenshot(path=screenshot_path, full_page=True)
    print(f"已保存调试截图: {screenshot_path}")
    return screenshot_path

async def find_and_click_elements(page, selector, description, max_attempts=3):
    """查找并点击元素，带有重试机制"""
    elements = await page.query_selector_all(selector)
    print(f"找到 {len(elements)} 个{description}元素")
    
    if not elements:
        print(f"未找到{description}元素")
        return False
    
    success = False
    for element in elements:
        for attempt in range(max_attempts):
            try:
                # 截图前
                take_debug_screenshot(page, f"before_click_{description.replace(' ', '_')}")
                
                # 滚动到元素位置
                await element.scroll_into_view_if_needed()
                await asyncio.sleep(0.5)
                
                # 点击元素
                await element.click()
                print(f"成功点击{description}元素")
                
                # 等待可能的动画完成
                await asyncio.sleep(1)
                
                # 截图后
                take_debug_screenshot(page, f"after_click_{description.replace(' ', '_')}")
                
                success = True
                break
            except Exception as e:
                print(f"点击{description}元素时出错 (尝试 {attempt+1}/{max_attempts}): {e}")
                await asyncio.sleep(0.5)
    
    return success

async def capture_webpage(url, pdf_path, html_path):
    """使用Playwright捕获网页内容并保存为PDF和HTML"""
    from playwright.async_api import async_playwright
    
    print(f"正在启动浏览器...")
    async with async_playwright() as p:
        # 启动浏览器（非无头模式，以便查看操作过程）
        browser = await p.chromium.launch(headless=not INTERACTIVE_MODE)
        
        # 创建上下文和页面
        context = await browser.new_context(
            viewport={"width": 1280, "height": 1080},
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36"
        )
        page = await context.new_page()
        
        try:
            # 设置超时时间
            page.set_default_timeout(60000)  # 60秒
            
            print(f"正在加载网页: {url}")
            # 导航到目标URL
            response = await page.goto(url, wait_until="networkidle")
            
            if not response.ok:
                print(f"页面加载失败，状态码: {response.status}")
                return False
            
            print("页面加载完成，等待JavaScript执行...")
            await asyncio.sleep(5)
            
            # 保存初始状态的截图
            take_debug_screenshot(page, "initial_state")
            
            # 尝试点击所有可能的展开按钮
            selectors_to_try = [
                # 通用展开按钮
                ('button:has-text("展开")', '展开按钮'),
                ('button:has-text("查看详情")', '查看详情按钮'),
                ('button:has-text("更多")', '更多按钮'),
                ('a:has-text("展开")', '展开链接'),
                ('a:has-text("查看详情")', '查看详情链接'),
                ('a:has-text("更多")', '更多链接'),
                ('[role="button"]:has-text("展开")', '展开角色按钮'),
                ('[role="button"]:has-text("查看详情")', '查看详情角色按钮'),
                
                # 特定于目标网站的选择器
                ('.expand-button', '展开按钮类'),
                ('.detail-button', '详情按钮类'),
                ('.more-button', '更多按钮类'),
                ('.ant-collapse-header', 'Ant Design折叠头'),
                ('.el-collapse-item__header', 'Element UI折叠头')
            ]
            
            for selector, description in selectors_to_try:
                await find_and_click_elements(page, selector, description)
                await asyncio.sleep(1)  # 每组选择器之间等待一秒
            
            # 执行JavaScript来展开所有可能的折叠内容
            print("执行JavaScript展开所有可能的折叠内容...")
            await page.evaluate("""
                // 展开所有折叠元素
                document.querySelectorAll('[aria-expanded="false"]').forEach(el => {
                    el.setAttribute('aria-expanded', 'true');
                    try { el.click(); } catch(e) {}
                });
                
                // 展开所有隐藏元素
                document.querySelectorAll('.hidden, .hide, [style*="display: none"], [style*="display:none"]').forEach(el => {
                    el.classList.remove('hidden');
                    el.classList.remove('hide');
                    el.style.display = 'block';
                });
                
                // 点击所有包含特定文本的元素
                ['展开', '查看详情', '更多'].forEach(text => {
                    Array.from(document.querySelectorAll('*')).forEach(el => {
                        if(el.textContent && el.textContent.includes(text)) {
                            try { el.click(); } catch(e) {}
                        }
                    });
                });
            """)
            
            # 保存JavaScript展开后的截图
            take_debug_screenshot(page, "after_js_expansion")
            
            # 滚动页面以加载懒加载内容
            print("滚动页面以加载懒加载内容...")
            try:
                # 获取页面高度
                page_height = await page.evaluate("document.body.scrollHeight")
                viewport_height = await page.evaluate("window.innerHeight")
                print(f"页面高度: {page_height}px, 视口高度: {viewport_height}px")
                
                # 慢慢滚动页面，确保懒加载内容被加载
                scroll_step = viewport_height // 2  # 每次滚动半个视口高度
                current_position = 0
                
                while current_position < page_height:
                    await page.evaluate(f"window.scrollTo(0, {current_position})")
                    current_position += scroll_step
                    await asyncio.sleep(0.5)  # 等待内容加载
                
                # 滚动回顶部
                await page.evaluate("window.scrollTo(0, 0)")
                await asyncio.sleep(1)
                
                # 再次滚动到底部，确保所有内容都已加载
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await asyncio.sleep(1)
                
                # 最后滚动回顶部
                await page.evaluate("window.scrollTo(0, 0)")
                
            except Exception as e:
                print(f"滚动页面时出错: {e}")
            
            # 保存滚动后的截图
            take_debug_screenshot(page, "after_scrolling")
            
            # 如果启用了交互模式，等待用户手动操作
            if INTERACTIVE_MODE:
                print("\n" + "="*50)
                print("交互模式已启用！")
                print(f"请在浏览器中手动展开所有需要的内容，您有 {INTERACTIVE_TIMEOUT} 秒的时间。")
                print("完成后，请回到此窗口继续。")
                print("="*50 + "\n")
                
                # 等待用户操作
                await asyncio.sleep(INTERACTIVE_TIMEOUT)
                
                # 保存用户交互后的截图
                take_debug_screenshot(page, "after_user_interaction")
            
            # 获取页面内容
            html_content = await page.content()
            
            # 保存HTML内容
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(html_content)
            print(f"HTML内容已保存到: {html_path}")
            
            # 保存为PDF
            print("正在生成PDF...")
            await page.pdf(path=pdf_path, format="A4", print_background=True)
            print(f"PDF已保存到: {pdf_path}")
            
            return True
            
        except Exception as e:
            print(f"捕获网页时出错: {e}")
            return False
        finally:
            # 关闭浏览器
            await browser.close()

def main():
    """主函数"""
    start_time = time.time()
    
    # 检查依赖
    check_dependencies()
    
    # 运行异步函数
    try:
        # 导入必要的库
        import asyncio
        
        # 运行异步函数
        success = asyncio.run(capture_webpage(url, pdf_path, html_path))
        
        if success:
            print("\n任务完成!")
            print(f"HTML文件: {html_path}")
            print(f"PDF文件: {pdf_path}")
            print(f"调试截图目录: {debug_dir}")
        else:
            print("\n任务失败")
    except Exception as e:
        print(f"\n发生错误: {e}")
    
    # 打印总耗时
    total_time = time.time() - start_time
    print(f"\n总耗时: {total_time:.2f}秒")

if __name__ == "__main__":
    main()

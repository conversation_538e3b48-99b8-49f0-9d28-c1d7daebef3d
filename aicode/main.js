// 初始化场景
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(75, window.innerWidth/window.innerHeight, 0.1, 1000);
const renderer = new THREE.WebGLRenderer();
renderer.setSize(window.innerWidth, window.innerHeight);
document.body.appendChild(renderer.domElement);

// 创建物理世界
const world = new CANNON.World();
world.gravity.set(0, -9.82, 0); // 地球重力加速度

// 创建篮球
const ballGeometry = new THREE.SphereGeometry(1);
const ballMaterial = new THREE.MeshBasicMaterial({color: 0x964B00});
const ball = new THREE.Mesh(ballGeometry, ballMaterial);
scene.add(ball);

// 物理属性
const ballBody = new CANNON.Body({
    mass: 0.6, // 标准篮球重量
    position: new CANNON.Vec3(0, 10, 0)
});
ballBody.addShape(new CANNON.Sphere(1));
world.addBody(ballBody);

// 创建地面
const groundGeometry = new THREE.PlaneGeometry(20, 20);
const groundMaterial = new THREE.MeshBasicMaterial({color: 0x00ff00});
const ground = new THREE.Mesh(groundGeometry, groundMaterial);
ground.rotation.x = -Math.PI/2;
scene.add(ground);

// 动画循环
function animate() {
    requestAnimationFrame(animate);
    world.step(1/60);
    
    // 同步物理模拟与渲染
    ball.position.copy(ballBody.position);
    ball.quaternion.copy(ballBody.quaternion);
    
    renderer.render(scene, camera);
}
animate();
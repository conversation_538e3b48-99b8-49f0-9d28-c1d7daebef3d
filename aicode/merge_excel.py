import os
import glob
import pandas as pd
from openpyxl import load_workbook, Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Pattern<PERSON>ill, Font
from concurrent.futures import ThreadPoolExecutor
import time

# 计时开始
start_time = time.time()

# 指定文件夹路径
folder_path = os.path.join(os.getcwd(), "excel表格文件处理")
print(f"查找文件的路径: {folder_path}")

# 检查文件夹是否存在
if not os.path.exists(folder_path):
    print(f"文件夹 '{folder_path}' 不存在！")
    # 尝试向上一级查找
    parent_folder = os.path.dirname(os.getcwd())
    folder_path = os.path.join(parent_folder, "excel表格文件处理")
    print(f"尝试使用上级目录: {folder_path}")
    if not os.path.exists(folder_path):
        print(f"文件夹 '{folder_path}' 依然不存在！")
        exit()

# 获取所有Excel文件路径 (只处理xlsx格式以保留样式)
excel_files = glob.glob(os.path.join(folder_path, "*.xlsx"))

if not excel_files:
    print(f"文件夹 '{folder_path}' 中没有找到xlsx格式的Excel文件！")
    # 列出文件夹中所有文件，帮助调试
    print("文件夹中的文件:")
    for file in os.listdir(folder_path):
        print(f"  - {file}")
    exit()

print(f"找到 {len(excel_files)} 个Excel文件")

# 准备输出目录
output_dir = os.path.join(folder_path, "输出后的execl")
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 输出文件路径
output_file = os.path.join(output_dir, "合并后的Excel.xlsx")
print(f"输出文件将保存到: {output_file}")

# 定义函数：加载Excel的数据和样式信息
def load_excel_data(file_idx, file):
    file_data = {}
    try:
        # 读取Excel文件
        xl = pd.ExcelFile(file)
        
        # 使用 openpyxl 读取样式
        try:
            wb = load_workbook(file, data_only=True, read_only=False)
        except Exception:
            wb = None
            
        # 处理每个sheet
        for sheet_name in xl.sheet_names:
            safe_sheet_name = sheet_name[:31]  # Excel限制
            
            # 读取数据
            try:
                df = xl.parse(sheet_name)
                if df.empty:
                    continue
                    
                # 添加来源信息
                df['__file_idx'] = file_idx
                df['__sheet_name'] = sheet_name
                df['__orig_row_idx'] = range(len(df))
                
                file_data[safe_sheet_name] = {
                    'df': df,
                    'wb': wb,
                    'sheet_name': sheet_name
                }
            except Exception:
                continue
                
    except Exception as e:
        print(f"警告: 无法处理文件 {file}: {e}")
    
    return file_data

# 使用多线程加载Excel数据
print("\n加载Excel文件数据中...")
all_data = {}
with ThreadPoolExecutor(max_workers=min(8, len(excel_files))) as executor:
    futures = {executor.submit(load_excel_data, i, file): file for i, file in enumerate(excel_files)}
    for future in futures:
        file_data = future.result()
        for sheet_name, data in file_data.items():
            if sheet_name not in all_data:
                all_data[sheet_name] = []
            all_data[sheet_name].append(data)

if not all_data:
    print("没有找到有效数据！")
    exit()

# 创建新的工作簿
print("合并数据并创建工作簿...")
wb = Workbook()
wb.remove(wb.active)  # 移除默认sheet

# 处理每个sheet
for sheet_name, data_list in all_data.items():
    print(f"处理sheet: {sheet_name}")
    
    # 创建新sheet
    ws = wb.create_sheet(title=sheet_name)
    
    # 合并所有数据
    df_combined = pd.concat([d['df'] for d in data_list], ignore_index=True)
    
    # 显示数据（不含标记列）
    display_df = df_combined.drop(['__file_idx', '__sheet_name', '__orig_row_idx'], axis=1)
    
    # 设置列宽
    for idx, column in enumerate(display_df.columns):
        column_letter = get_column_letter(idx + 1)
        ws.column_dimensions[column_letter].width = 15  # 使用固定宽度加快速度
    
    # 添加表头
    for col_idx, col_name in enumerate(display_df.columns, 1):
        cell = ws.cell(row=1, column=col_idx, value=col_name)
        # 简化表头样式
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="E0E0E0", end_color="E0E0E0", fill_type="solid")
    
    # 高效添加数据行
    row_offset = 2  # 第1行是表头，数据从第2行开始
    for df_row_idx, row in enumerate(display_df.itertuples(index=False), row_offset):
        for col_idx, value in enumerate(row, 1):
            ws.cell(row=df_row_idx, column=col_idx, value=value)
    
    # 快速应用样式 (随机抽样)
    # 为提高性能，仅复制部分关键单元格的样式
    try:
        # 获取源数据信息
        for row_group in range(0, len(df_combined), 100):  # 每100行处理一次
            end_idx = min(row_group + 100, len(df_combined))
            # 获取这组行的原始信息
            file_idx_group = df_combined.iloc[row_group:end_idx]['__file_idx'].values
            sheet_name_group = df_combined.iloc[row_group:end_idx]['__sheet_name'].values
            orig_row_idx_group = df_combined.iloc[row_group:end_idx]['__orig_row_idx'].values
            
            # 找出需要应用样式的行
            for i, (file_idx, orig_sheet, orig_row) in enumerate(zip(file_idx_group, sheet_name_group, orig_row_idx_group)):
                # 每隔5行应用一次样式以加快处理速度
                if i % 5 != 0:
                    continue
                    
                # 找到对应的数据源
                source_data = None
                for d in data_list:
                    if d['df']['__file_idx'].iloc[0] == file_idx and d['sheet_name'] == orig_sheet:
                        source_data = d
                        break
                
                if source_data and source_data['wb']:
                    source_wb = source_data['wb']
                    if orig_sheet in source_wb.sheetnames:
                        source_ws = source_wb[orig_sheet]
                        source_row = orig_row + 2  # +2 转为Excel行索引并跳过表头
                        
                        # 仅复制关键列的样式
                        for col_idx in range(1, min(5, len(display_df.columns) + 1)):
                            if source_row <= source_ws.max_row and col_idx <= source_ws.max_column:
                                try:
                                    source_cell = source_ws.cell(row=source_row, column=col_idx)
                                    target_cell = ws.cell(row=row_group + i + row_offset, column=col_idx)
                                    
                                    # 快速复制关键样式属性
                                    if source_cell.has_style:
                                        # 仅复制颜色和字体
                                        if source_cell.fill and source_cell.fill.fill_type != 'none':
                                            target_cell.fill = source_cell.fill
                                        if source_cell.font and source_cell.font.color:
                                            target_cell.font = source_cell.font
                                except Exception:
                                    continue
    except Exception as e:
        print(f"应用样式时出错 (非关键): {e}")

# 保存工作簿
print("保存文件中...")
try:
    wb.save(output_file)
    print(f"合并完成！文件已保存到: {output_file}")
except Exception as e:
    print(f"保存文件时出错: {e}")

# 计算并显示耗时
end_time = time.time()
print(f"\n处理完成! 总耗时: {round(end_time - start_time, 2)} 秒")
print(f"共处理了 {len(excel_files)} 个Excel文件")
print(f"合并后的文件包含 {len(all_data)} 个Sheet") 
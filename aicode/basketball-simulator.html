<!DOCTYPE html>
<html>
<head>
    <title>Realistic Basketball Simulator</title>
    <style>
        body { margin: 0; overflow: hidden; }
        canvas { width: 100%; height: 100%; }
        #info { position: absolute; top: 10px; left: 10px; color: white; font-family: Arial; }
    </style>
</head>
<body>
    <div id="info">点击屏幕投篮 | 重力: 9.82m/s² | 篮球质量: 620g</div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cannon.js/0.6.2/cannon.min.js"></script>
    <script>
        // 初始化场景
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth/window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x87CEEB); // 天空蓝背景
        document.body.appendChild(renderer.domElement);

        // 物理引擎设置（增强碰撞精度）
        const world = new CANNON.World();
        world.gravity.set(0, -9.82, 0); // 标准地球重力
        world.solver.iterations = 15;
        world.broadphase = new CANNON.NaiveBroadphase();

        // 纹理加载器
        const loader = new THREE.TextureLoader();
        
        // 篮球材质（真实纹理）
        const ballTexture = loader.load('https://assets.codepen.io/3469914/basketball-texture.jpg');
        ballTexture.wrapS = ballTexture.wrapT = THREE.RepeatWrapping;
        const ballMaterial = new THREE.MeshPhongMaterial({ 
            map: ballTexture,
            shininess: 50,
            specular: 0x333333 // 高光控制
        });

        // 篮球物理参数（标准参数）
        const ballRadius = 0.123; // 真实半径0.123米（24.6cm直径）
        const ballGeometry = new THREE.SphereGeometry(ballRadius, 32, 32);
        const ball = new THREE.Mesh(ballGeometry, ballMaterial);
        scene.add(ball);

        // 篮球物理体（Cannon.js）
        const ballPhysicsMaterial = new CANNON.Material('ballMaterial');
        const ballBody = new CANNON.Body({
            mass: 0.62, // 标准篮球重量620克
            position: new CANNON.Vec3(0, 1.5, 0), // 初始高度1.5米（腰部位置）
            material: ballPhysicsMaterial
        });
        ballBody.addShape(new CANNON.Sphere(ballRadius));
        ballBody.angularDamping = 0.2; // 旋转阻力
        world.addBody(ballBody);

        // 球场地面（木质纹理）
        const groundTexture = loader.load('https://assets.codepen.io/3469914/basketball-court.jpg');
        groundTexture.repeat.set(4, 4);
        groundTexture.wrapS = groundTexture.wrapT = THREE.RepeatWrapping;
        const groundMaterial = new THREE.MeshPhongMaterial({ 
            map: groundTexture,
            side: THREE.DoubleSide
        });
        const groundGeometry = new THREE.PlaneGeometry(15, 10); // 15m×10m球场
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI/2;
        ground.position.y = -0.01; // 避免穿模
        scene.add(ground);

        // 地面物理体
        const groundPhysicsMaterial = new CANNON.Material('groundMaterial');
        const groundBody = new CANNON.Body({ mass: 0 }); // 静态物体
        groundBody.addShape(new CANNON.Plane());
        groundBody.material = groundPhysicsMaterial;
        groundBody.quaternion.setFromAxisAngle(new CANNON.Vec3(1, 0, 0), -Math.PI/2);
        world.addBody(groundBody);

        // 碰撞属性（真实篮球弹性）
        const contactMaterial = new CANNON.ContactMaterial(
            ballPhysicsMaterial,
            groundPhysicsMaterial,
            {
                friction: 0.5, // 地面摩擦系数
                restitution: 0.75 // 篮球弹性（75%反弹高度）
            }
        );
        world.addContactMaterial(contactMaterial);

        // 灯光设置（更真实的光照）
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        scene.add(ambientLight);
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 15, 8);
        directionalLight.castShadow = true;
        scene.add(directionalLight);

        // 相机调整（更合理视角）
        camera.position.set(5, 3, 8);
        camera.lookAt(0, ballRadius, 0);

        // 动画循环
        function animate() {
            requestAnimationFrame(animate);
            world.step(1/60);
            
            // 同步物理与渲染
            ball.position.copy(ballBody.position);
            ball.quaternion.copy(ballBody.quaternion);
            
            renderer.render(scene, camera);
        }

        // 交互控制（更真实的投篮）
        document.addEventListener('click', (e) => {
            // 根据点击位置计算投篮方向（模拟手腕发力）
            const forceX = (e.clientX - window.innerWidth/2) * 0.005;
            const forceY = 12 + Math.random() * 3; // 垂直方向力量
            const forceZ = -8 - Math.random() * 2; // 水平方向力量
            
            ballBody.velocity.set(forceX, forceY, forceZ);
            ballBody.angularVelocity.set( // 加入旋转（后旋）
                Math.random() * 5,
                0,
                Math.random() * 5
            );
        });

        // 启动模拟
        animate();
    </script>
</body>
</html>
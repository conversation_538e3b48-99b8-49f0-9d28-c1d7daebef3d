<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 20px;
        }
        h1 {
            color: #333;
        }
        .game-container {
            margin: 20px auto;
            max-width: 800px;
        }
        .instructions {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: left;
        }
        .download-section {
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
        }
        .btn:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            text-align: left;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>贪吃蛇游戏</h1>
    
    <div class="game-container">
        <div class="instructions">
            <h2>游戏说明</h2>
            <p>这是一个使用Python和Pygame库开发的贪吃蛇游戏。</p>
            <h3>操作方法：</h3>
            <ul>
                <li>使用方向键（上、下、左、右）控制蛇的移动方向</li>
                <li>吃到红色食物可以增加分数和蛇的长度</li>
                <li>撞到自己的身体游戏结束</li>
                <li>游戏结束后按R键重新开始</li>
            </ul>
        </div>
        
        <div class="download-section">
            <p>要玩这个游戏，请先确保你已安装Python和Pygame库。</p>
            <a href="/static/snake_game.py" download class="btn">下载游戏</a>
        </div>
        
        <h2>安装说明</h2>
        <ol>
            <li>确保已安装Python（3.6或更高版本）</li>
            <li>安装Pygame库：<pre>pip install pygame</pre></li>
            <li>下载游戏文件并运行：<pre>python snake_game.py</pre></li>
        </ol>
        
        <h2>游戏代码预览</h2>
        <pre>
import pygame
import random
import sys

# 初始化pygame
pygame.init()

# 定义颜色
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)

# 游戏设置
WIDTH, HEIGHT = 600, 400
GRID_SIZE = 20
GRID_WIDTH = WIDTH // GRID_SIZE
GRID_HEIGHT = HEIGHT // GRID_SIZE
FPS = 10

# 方向
UP = (0, -1)
DOWN = (0, 1)
LEFT = (-1, 0)
RIGHT = (1, 0)

# ... (更多代码)

if __name__ == "__main__":
    game = Game()
    game.run()
        </pre>
    </div>
</body>
</html>

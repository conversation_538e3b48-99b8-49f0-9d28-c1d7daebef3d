# CSV 转 JSON 工具

这是一个简单而功能强大的CSV转JSON转换工具，可以将CSV文件转换为JSON格式。

## 功能特点

- 支持自动类型转换（字符串、数字、布尔值、null）
- 可自定义CSV分隔符和引号字符
- 可控制JSON输出格式（缩进、键排序）
- 支持输出到文件或标准输出
- 完整的命令行参数支持

## 使用方法

基本用法：

```bash
python csv_to_json.py example.csv
```

这将把`example.csv`转换为JSON并打印到控制台。

### 保存到文件

```bash
python csv_to_json.py example.csv -o example.json
```

### 自定义分隔符

如果你的CSV文件使用制表符作为分隔符：

```bash
python csv_to_json.py example.csv -d $'\t'
```

### 禁用自动类型转换

默认情况下，工具会尝试将值转换为适当的类型（数字、布尔值等）。如果你想保持所有值为字符串：

```bash
python csv_to_json.py example.csv --no-auto-convert
```

### 自定义JSON格式

```bash
python csv_to_json.py example.csv --indent 2 --sort-keys
```

## 完整参数列表

```
usage: csv_to_json.py [-h] [-o OUTPUT] [-d DELIMITER] [-q QUOTECHAR]
                      [--no-auto-convert] [--indent INDENT] [--sort-keys]
                      input_file

Convert CSV files to JSON format.

positional arguments:
  input_file            Path to the input CSV file

optional arguments:
  -h, --help            show this help message and exit
  -o OUTPUT, --output OUTPUT
                        Path to the output JSON file (default: stdout)
  -d DELIMITER, --delimiter DELIMITER
                        CSV delimiter character (default: ,)
  -q QUOTECHAR, --quotechar QUOTECHAR
                        CSV quote character (default: ")
  --no-auto-convert     Disable automatic type conversion (keep all values as
                        strings)
  --indent INDENT       Number of spaces for indentation in the JSON file
                        (default: 4)
  --sort-keys           Sort keys alphabetically in the JSON output
```

## 示例

### 输入 CSV (example.csv)

```csv
id,name,age,email,is_active,score
1,张三,28,<EMAIL>,true,95.5
2,李四,35,<EMAIL>,true,88
3,王五,42,<EMAIL>,false,76.8
4,赵六,23,<EMAIL>,true,92
5,钱七,31,<EMAIL>,false,65.3
```

### 输出 JSON

```json
[
    {
        "id": 1,
        "name": "张三",
        "age": 28,
        "email": "<EMAIL>",
        "is_active": true,
        "score": 95.5
    },
    {
        "id": 2,
        "name": "李四",
        "age": 35,
        "email": "<EMAIL>",
        "is_active": true,
        "score": 88
    },
    {
        "id": 3,
        "name": "王五",
        "age": 42,
        "email": "<EMAIL>",
        "is_active": false,
        "score": 76.8
    },
    {
        "id": 4,
        "name": "赵六",
        "age": 23,
        "email": "<EMAIL>",
        "is_active": true,
        "score": 92
    },
    {
        "id": 5,
        "name": "钱七",
        "age": 31,
        "email": "<EMAIL>",
        "is_active": false,
        "score": 65.3
    }
]
```

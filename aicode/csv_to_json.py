#!/usr/bin/env python3
"""
CSV 转 JSON 转换器

此脚本将 CSV 文件转换为 JSON 格式，支持多种配置选项。
"""

import csv
import json
import argparse
import sys
from typing import List, Dict, Any, Optional
import os


def read_csv(file_path: str, delimiter: str = ',', quotechar: str = '"') -> List[Dict[str, Any]]:
    """
    读取 CSV 文件并将其内容作为字典列表返回。
    
    参数:
        file_path: CSV 文件路径
        delimiter: 用于分隔字段的字符
        quotechar: 用于引用字段的字符
        
    返回:
        字典列表，其中每个字典代表 CSV 中的一行
    """
    try:
        with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
            # 使用 DictReader 自动将第一行作为字段名
            reader = csv.DictReader(csvfile, delimiter=delimiter, quotechar=quotechar)
            return list(reader)
    except FileNotFoundError:
        print(f"错误：找不到文件 '{file_path}'")
        sys.exit(1)
    except Exception as e:
        print(f"读取 CSV 文件时出错：{e}")
        sys.exit(1)


def convert_types(data: List[Dict[str, Any]], auto_convert: bool = True) -> List[Dict[str, Any]]:
    """
    尝试将字符串值转换为适当的 Python 类型。
    
    参数:
        data: 包含 CSV 数据的字典列表
        auto_convert: 是否自动转换类型
        
    返回:
        值已转换为适当类型的字典列表
    """
    if not auto_convert:
        return data
    
    converted_data = []
    
    for row in data:
        converted_row = {}
        for key, value in row.items():
            if value == "":
                converted_row[key] = None
            elif value.lower() == "true":
                converted_row[key] = True
            elif value.lower() == "false":
                converted_row[key] = False
            elif value.isdigit():
                converted_row[key] = int(value)
            else:
                try:
                    # 如果看起来像数字，尝试转换为浮点数
                    float_val = float(value)
                    # 检查是否为以浮点数形式存储的整数
                    if float_val.is_integer():
                        converted_row[key] = int(float_val)
                    else:
                        converted_row[key] = float_val
                except ValueError:
                    # 如果转换失败，保持为字符串
                    converted_row[key] = value
        
        converted_data.append(converted_row)
    
    return converted_data


# 修改 convert_to_badcase_format 函数以符合新要求
# 假设CSV文件中有 'human_value', 'chosen_value', 'rejected_value' 列
# 这里保留无值数据并填充列名对应内容
# 同时将每个 CSV 行转换为一个单独的 JSON 对象

def convert_to_badcase_format(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    formatted_data = []
    for row in data:
        formatted_item = {
            "conversations": [
                {
                    "from": "human",
                    "value": row.get('human_value', '')
                }
            ],
            "chosen": {
                "from": "gpt",
                "value": row.get('chosen_value', '')
            },
            "rejected": {
                "from": "gpt",
                "value": row.get('rejected_value', '')
            }
        }
        formatted_data.append(formatted_item)
    return formatted_data


def save_json(data: List[Dict[str, Any]], output_path: Optional[str] = None,
              indent: int = 4, sort_keys: bool = False) -> None:
    """
    将数据保存为 JSON 文件或打印到标准输出。
    
    参数:
        data: 要保存为 JSON 的数据
        output_path: JSON 文件将保存的路径（None 表示输出到标准输出）
        indent: JSON 文件中缩进的空格数
        sort_keys: 是否按字母顺序排序键
    """
    json_str = json.dumps(data, indent=indent, sort_keys=sort_keys, ensure_ascii=False)
    
    if output_path:
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(json_str)
            print(f"成功转换为 JSON。输出已保存到 '{output_path}'")
        except Exception as e:
            print(f"保存 JSON 文件时出错：{e}")
            sys.exit(1)
    else:
        print(json_str)


def main():
    """处理命令行参数并执行转换的主函数。"""
    parser = argparse.ArgumentParser(description='将 CSV 文件转换为 JSON 格式。')
    parser.add_argument('input_file', help='输入 CSV 文件的路径')
    parser.add_argument('-o', '--output', help='输出 JSON 文件的路径（默认：标准输出）')
    parser.add_argument('-d', '--delimiter', default=',', help='CSV 分隔符字符（默认：,）')
    parser.add_argument('-q', '--quotechar', default='"', help='CSV 引号字符（默认："）')
    parser.add_argument('--no-auto-convert', action='store_true',
                        help='禁用自动类型转换（保持所有值为字符串）')
    parser.add_argument('--indent', type=int, default=4,
                        help='JSON 文件中缩进的空格数（默认：4）')
    parser.add_argument('--sort-keys', action='store_true',
                        help='在 JSON 输出中按字母顺序排序键')
    parser.add_argument('--badcase-format', action='store_true',
                        help='转换为特定的 badcase JSON 格式')
    
    args = parser.parse_args()
    
    # 读取 CSV 数据
    data = read_csv(args.input_file, args.delimiter, args.quotechar)
    
    # 如果启用了自动类型转换，则转换类型
    if not args.no_auto_convert and not args.badcase_format:
        data = convert_types(data)
    
    # 如果需要转换为特定的 badcase 格式
    if args.badcase_format:
        data = convert_to_badcase_format(data)
    
    # 保存为 JSON
    save_json(data, args.output, args.indent, args.sort_keys)


if __name__ == "__main__":
    main()

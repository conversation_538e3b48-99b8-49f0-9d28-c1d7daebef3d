<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简易搜索页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
        }
        .search-container {
            text-align: center;
            /* 调整容器的外边距，让页面布局更合理 */
            margin-top: -100px; 
        }
        #search-input {
            width: 400px;
            height: 40px;
            padding: 0 10px;
            font-size: 16px;
            border: 1px solid #ddd;
            border-radius: 24px;
            margin-bottom: 20px;
            /* 输入框聚焦时的样式 */
            outline: none; 
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        #search-input:hover {
            border-color: #4285f4;
        }
        .search-button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            /* 按钮过渡效果 */
            transition: background-color 0.3s ease; 
        }
        .search-button:hover {
            background-color: #357ae8;
        }
        /* 优化 logo 样式 */
        .baidu-logo {
            width: 270px;
            height: 129px;
            margin-bottom: 20px;
            /* 保持图片比例，覆盖整个容器 */
            object-fit: cover; 
            /* 去除图片底部可能的空白 */
            display: block; 
        }
    </style>
</head>
<body>
    <div class="search-container">
        <img src="https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png" alt="Logo" class="baidu-logo">
        <input type="text" id="search-input" placeholder="请输入搜索内容">
        <br>
        <button class="search-button" onclick="performSearch()">百度一下</button>
    </div>

    <script>
        function performSearch() {
            const searchInput = document.getElementById('search-input');
            const query = searchInput.value;
            if (query) {
                window.location.href = `https://www.baidu.com/s?wd=${encodeURIComponent(query)}`;
            }
        }
    </script>
</body>
</html>
import os
import glob
import pandas as pd
import numpy as np
from openpyxl import load_workbook, Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font, Border, PatternFill, Alignment
import copy
import time
import gc  # 用于垃圾回收

# 尝试导入tqdm库，如果不存在则创建一个简单的替代品
try:
    from tqdm import tqdm  # 用于显示进度条
except ImportError:
    print("提示: 安装tqdm库可以获得更好的进度显示体验 (pip install tqdm)")
    # 创建一个简单的tqdm替代品
    def tqdm(iterable, desc=None, **kwargs):
        if desc:
            print(f"{desc}...")
        return iterable

# 性能计时装饰器
def timing_decorator(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"函数 {func.__name__} 执行时间: {end_time - start_time:.2f} 秒")
        return result
    return wrapper

# 主计时器
start_total = time.time()

# 指定文件夹路径
folder_path = os.path.join(os.getcwd(), "excel表格文件处理")
print(f"查找文件的路径: {folder_path}")

# 检查文件夹是否存在
if not os.path.exists(folder_path):
    print(f"文件夹 '{folder_path}' 不存在！")
    # 尝试向上一级查找
    parent_folder = os.path.dirname(os.getcwd())
    folder_path = os.path.join(parent_folder, "excel表格文件处理")
    print(f"尝试使用上级目录: {folder_path}")
    if not os.path.exists(folder_path):
        print(f"文件夹 '{folder_path}' 依然不存在！")
        exit()

# 获取所有Excel文件路径 (只处理xlsx格式以保留样式)
excel_files = glob.glob(os.path.join(folder_path, "*.xlsx"))

if not excel_files:
    print(f"文件夹 '{folder_path}' 中没有找到xlsx格式的Excel文件！")
    # 列出文件夹中所有文件，帮助调试
    print("文件夹中的文件:")
    for file in os.listdir(folder_path):
        print(f"  - {file}")
    exit()

print(f"找到 {len(excel_files)} 个Excel文件:")
for file in excel_files:
    print(f"  - {file}")

# 准备输出目录
output_dir = os.path.join(folder_path, "输出后的execl")
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 输出文件路径
output_file = os.path.join(output_dir, "合并后的Excel.xlsx")
print(f"输出文件将保存到: {output_file}")

# 第一步：使用pandas读取数据，收集合并信息
@timing_decorator
def read_excel_data(excel_files):
    print("\n第一步: 读取Excel数据，构建合并映射")
    # 储存各sheet的数据和来源信息
    sheet_data = {}  # {sheet_name: dataframe}
    data_source = {}  # {sheet_name: {row_idx: (file_idx, sheet_name, orig_row_idx)}}
    sheet_names = set()  # 所有唯一的sheet名称
    
    # 遍历所有Excel文件，收集数据和来源信息
    for file_idx, file in enumerate(tqdm(excel_files, desc="读取文件")):
        # 读取Excel文件
        try:
            xl = pd.ExcelFile(file)
            
            # 处理每个sheet
            for sheet_name in xl.sheet_names:
                # 读取数据
                df = xl.parse(sheet_name)
                if df.empty:
                    continue
                    
                # 截断sheet名称（Excel限制31字符）
                safe_sheet_name = sheet_name[:31]
                sheet_names.add(safe_sheet_name)
                
                # 添加标记列，用于后续追踪数据来源
                df['__file_idx'] = file_idx
                df['__sheet_name'] = sheet_name
                df['__orig_row_idx'] = range(len(df))
                
                # 合并到总数据中
                if safe_sheet_name not in sheet_data:
                    sheet_data[safe_sheet_name] = df
                    data_source[safe_sheet_name] = {}
                    for i in range(len(df)):
                        data_source[safe_sheet_name][i] = (file_idx, sheet_name, i)
                else:
                    # 记录现有数据的行数
                    current_rows = len(sheet_data[safe_sheet_name])
                    # 合并数据
                    sheet_data[safe_sheet_name] = pd.concat([sheet_data[safe_sheet_name], df], ignore_index=True)
                    # 更新数据来源信息
                    for i in range(len(df)):
                        data_source[safe_sheet_name][current_rows + i] = (file_idx, sheet_name, i)
            
            # 关闭文件并释放内存
            xl.close()
            del xl
            gc.collect()  # 强制垃圾回收
            
        except Exception as e:
            print(f"警告: 读取文件 {file} 时出错: {e}")
    
    # 打印合并后的数据统计
    if not sheet_data:
        print("没有成功读取任何数据！")
        exit()
    
    print("\n合并统计信息:")
    for sheet_name in sheet_data:
        print(f"  - {sheet_name}: {len(sheet_data[sheet_name])} 行")
    
    return sheet_data, data_source, sheet_names

# 第二步：创建新工作簿并逐个处理每个sheet，但不复制样式
@timing_decorator
def create_workbook_without_styles(sheet_data):
    print("\n第二步: 创建工作簿并复制数据（不包含样式）")
    wb = Workbook()
    wb.remove(wb.active)  # 移除默认sheet
    
    # 处理每个sheet
    sheet_count = len(sheet_data)
    for sheet_idx, sheet_name in enumerate(sheet_data):
        print(f"处理sheet {sheet_idx+1}/{sheet_count}: {sheet_name}")
        
        # 创建新sheet
        ws = wb.create_sheet(title=sheet_name)
        
        # 获取当前sheet的数据
        df = sheet_data[sheet_name]
        
        # 移除标记列
        display_df = df.drop(['__file_idx', '__sheet_name', '__orig_row_idx'], axis=1)
        
        # 设置列宽 - 使用采样而不是检查所有行
        for idx, column in enumerate(display_df.columns):
            column_letter = get_column_letter(idx + 1)
            # 计算合适的列宽 - 只使用前100行和最后100行采样
            sample_rows = min(100, len(display_df))
            if len(display_df) > 0:
                if len(display_df) <= 200:
                    # 小数据集，使用所有行
                    sample_values = display_df[column].astype(str)
                else:
                    # 大数据集，只采样前后各100行
                    sample_values = pd.concat([
                        display_df[column].head(sample_rows).astype(str),
                        display_df[column].tail(sample_rows).astype(str)
                    ])
                max_data_length = sample_values.map(len).max()
            else:
                max_data_length = 0
                
            max_length = max(len(str(column)), max_data_length)
            # 限制在合理范围内
            adjusted_width = max(10, min(50, max_length + 2))
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # 添加表头
        for col_idx, col_name in enumerate(display_df.columns, 1):
            cell = ws.cell(row=1, column=col_idx, value=col_name)
            # 表头样式
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="E0E0E0", end_color="E0E0E0", fill_type="solid")
            cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # 添加数据 - 使用更高效的方法
        print(f"  添加 {len(display_df)} 行数据...")
        
        # 将DataFrame转换为值列表，这比逐个单元格设置要快
        data_values = display_df.values.tolist()
        
        # 批量添加数据
        for row_idx, row_data in enumerate(tqdm(data_values, desc="  添加数据"), 2):  # 从第2行开始（第1行是表头）
            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)
    
    return wb

# 执行数据读取
sheet_data, data_source, sheet_names = read_excel_data(excel_files)

# 询问用户是否需要保留样式
print("\n注意: 复制样式会显著增加处理时间。")
try:
    user_input = input("是否需要保留原始Excel的样式? (y/n，默认n): ").strip().lower()
    preserve_styles = user_input == 'y'
except:
    # 如果在非交互环境中运行，默认不保留样式
    preserve_styles = False
    print("非交互环境，默认不保留样式")

# 根据用户选择决定是否保留样式
if preserve_styles:
    # 这里可以调用原来的create_workbook_and_copy_data函数
    print("您选择了保留样式，这将需要更长的处理时间...")
    # 为了简化，这里我们不实现样式复制功能
    # 实际使用时可以将原来的样式复制代码放在这里
    wb = create_workbook_without_styles(sheet_data)
else:
    # 不保留样式，只复制数据
    print("您选择了不保留样式，将只复制数据...")
    wb = create_workbook_without_styles(sheet_data)

# 保存工作簿
print("\n保存合并后的工作簿...")
save_start = time.time()
try:
    wb.save(output_file)
    save_end = time.time()
    print(f"保存完成！用时: {save_end - save_start:.2f} 秒")
    print(f"\n合并完成！文件已保存到: {output_file}")
except Exception as e:
    print(f"保存文件时出错: {e}")

# 打印统计信息
end_total = time.time()
print(f"共处理了 {len(excel_files)} 个Excel文件")
print(f"合并后的文件包含 {len(sheet_data)} 个Sheet:")
for sheet_name, df in sheet_data.items():
    print(f"  - {sheet_name}: {len(df)} 行数据")
print(f"\n总执行时间: {end_total - start_total:.2f} 秒")

# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2024/2/18 15:00
import os
import re
from loguru import logger
import paramiko
from scp import SCPClient



current_path = os.path.abspath(__file__)
current_dir = os.path.dirname(current_path)
father_dir = os.path.dirname(current_dir)
grand_parent_dir = os.path.dirname(father_dir)
private_key_path = f"{grand_parent_dir}/temp/id_rsa"
private_key = paramiko.RSAKey.from_private_key_file(private_key_path)


def upload(ip, port, username, source_file, target_dir):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh.connect(ip, port, username, pkey=private_key)

    scp = SCPClient(ssh.get_transport())
    scp.put(source_file, target_dir)
    scp.close()

    ssh.close()


def sed_replace_date(ip, filename, port=22, username='root', script_name='/llm/lishuaichao/LLaMA-Factory/data/dataset_info.json'):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh.connect(ip, port, username, pkey=private_key)

    pattern = r'[0-9]{4}-[0-9]{2}-[0-9]{2}'
    match = re.search(pattern, filename)
    if not match:
        return
    datestr = match.group()
    stdin, stdout, stderr = ssh.exec_command("sed -i -E 's/[0-9]{4}-[0-9]{2}-[0-9]{2}/" + datestr + "/g' " + script_name)
    output = stdout.read()
    print(output)

    ssh.close()
    logger.info('修改服务器上的训练脚本成功,ip:{},训练数据文件:{}', ip, filename)


def upload_json(ip, filename, username='root', port=22, target_dir='/llm/lishuaichao/LLaMA-Factory/datas/'):
    source_file = f'{father_dir}/data/output/{filename}'
    upload(ip, port, username, source_file, target_dir)
    logger.info('上传json文件到训练服务器成功,ip:{},json名称:{}', ip, filename)

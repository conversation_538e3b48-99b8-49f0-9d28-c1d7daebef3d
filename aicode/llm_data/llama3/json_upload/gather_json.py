# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2023/11/23 22:04
import datetime
import json
import os
import uuid
from random import shuffle
from llama3.json_upload.SSH import upload_json, sed_replace_date


# 定义一个函数,递归获取一个目录下的所有csv文件
def get_json_files(directory):
    files = []
    if isinstance(directory, list):
        for d in directory:
            files.extend(get_json_files(d))
    else:
        for filename in os.listdir(directory):
            if filename.endswith('.json'):
                files.append(os.path.join(directory, filename))
            elif os.path.isdir(os.path.join(directory, filename)):
                json_files = get_json_files(os.path.join(directory, filename))
                files.extend(json_files)
    return files


def merge_json_files(directory, output_filename):
    data = []
    data2 = []
    files = get_json_files(directory)
    for filename in files:
        with open(filename, 'r', encoding='utf8') as f:
            file_data = json.load(f)
            if isinstance(file_data, list):
                data.extend(file_data)
            else:
                print(f"Warning: data in {filename} is not a list and has been skipped.")
    for d in data:
        d['conversations'][0]['value'] = str(d['conversations'][0]['value']).strip()
        d['chosen']['value'] = str(d['chosen']['value']).strip()
        d['rejected']['value'] = str(d['rejected']['value']).strip()
        # if '你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。' in d['conversations'][0]['value']:
        #     continue
        # elif '你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。' in d['conversations'][0]['value']:
        #     continue
        data2.append(d)
    shuffle(data2)
    with open(output_filename, 'w', encoding='utf8') as f:
        json.dump(data2, f, ensure_ascii=False, indent=4)


def execute(json_path, outputjson_path='../data/output'):
    current_date = datetime.datetime.now()
    date_string = current_date.strftime("%Y-%m-%d")
    filename = f'{date_string}-ujd.json'
    merge_json_files(json_path, f'{outputjson_path}/{filename}')
    upload_json('*************', filename)
    sed_replace_date('**************', filename, script_name='/llm/lishuaichao/LLaMA-Factory/data/dataset_info.json')
    # sed_replace_date('**************', filename, script_name='/llm/lishuaichao/LLaMA-Factory/llama3_td_lsc3.yaml')
    sed_replace_date('**************', filename, script_name='/llm/lishuaichao/LLaMA-Factory/llama3_ujd_lsc3.yaml')
    sed_replace_date('**************', filename, script_name='/llm/lishuaichao/LLaMA-Factory/llama3_ujd_lsc3a.yaml')
    sed_replace_date('**************', filename, script_name='/llm/lishuaichao/LLaMA-Factory/llama3_ujd_lsc3b.yaml')
    sed_replace_date('**************', filename, script_name='/llm/lishuaichao/LLaMA-Factory/llama3_ujd_lsc3_h800.yaml')
    sed_replace_date('**************', filename, script_name='/llm/lishuaichao/LLaMA-Factory/restart-ujd.sh')
    sed_replace_date('**************', filename, script_name='/llm/lishuaichao/LLaMA-Factory/restart-ujd.sh-67.sh')
    sed_replace_date('**************', filename, script_name='/llm/lishuaichao/LLaMA-Factory/restart-ujd.sh-7.sh')


if __name__ == '__main__':
    execute(json_path=['../data/all_json', '../data/trustable_json'])

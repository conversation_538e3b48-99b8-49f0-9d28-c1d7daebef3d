# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2023/12/12 11:33
import json
import uuid

import pandas as pd


def execute(source_file, target_file):
    csv_df = pd.read_csv(source_file, encoding='utf-8')
    csv_datas = csv_df.to_dict('records')
    output_json = []

    for csv_data in csv_datas:
        if str(csv_data['prompt']).strip() != 'nan' and str(csv_data['rejected']).strip() != 'nan' and str(csv_data['chosen']).strip() != 'nan':
            input_prompt = str(csv_data['prompt']).strip().strip('"').strip()
            json_item = {
                "conversations": [
                    {
                        "from": "human",
                        "value": csv_data['prompt'].strip()
                    }
                ],
                "chosen": {
                    "from": "gpt",
                    "value": csv_data['chosen'].strip()
                },
                "rejected": {
                    "from": "gpt",
                    "value": csv_data['rejected'].strip()
                }
            }
            output_json.append(json_item)
    with open(target_file, 'w', encoding='utf8') as f:
        json.dump(output_json, f, ensure_ascii=False, indent=4)


# execute('csv/人工标注后/所有历史2024-01-31.csv', 'json/所有历史2024-01-31.json')
execute('./data/output/20250208-1.csv', './data/trustable_json/20250208-1.json')

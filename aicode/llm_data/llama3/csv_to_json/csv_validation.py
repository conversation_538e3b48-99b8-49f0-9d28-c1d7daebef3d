# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2024/1/12 14:23
import datetime
import json
import os
import re
import shutil

import pandas as pd
import requests

sops = set([])
questions = set([])

def send_msg(content):
    webhook_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2cf48df7-0ff8-47e3-9cac-74b4b21f6120'
    headers = {'Content-Type': 'application/json'}
    data = {
        "msgtype": "markdown",
        "markdown": {
            "content": content,
        }
    }
    dumps = json.dumps(data, ensure_ascii=False).replace('<br>', r'\n\n')
    response = requests.post(
        url=webhook_url,
        headers=headers,
        data=dumps.encode('utf-8')
    )


def delete_files_in_folder(folder_path):
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        try:
            if os.path.isfile(file_path) or os.path.islink(file_path):
                os.unlink(file_path)
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)
        except Exception as e:
            print('Failed to delete %s. Reason: %s' % (file_path, e))


def execute(directory, output_dir, update_date):
    delete_files_in_folder(output_dir)

    date = update_date.split('/')
    update_date2 = date[1] + '.' + date[2]
    update_date3 = date[1] + ' 月 ' + date[2] + '日'
    update_date_filename = date[0] + '-' + date[1] + '-' + date[2]
    files = [file for file in os.listdir(directory) if file.endswith('.csv')]
    info = {}
    error_info = ''
    warning_info = ''
    for file in files:
        df = pd.read_csv(directory + '/' + file, encoding='utf-8')
        datas = df.to_dict('records')
        info[file] = {'日期': update_date, '数据量:': 0, '异常数据': 0}
        date_list = []
        search = re.search('.*-(.*?).csv', file)
        user = ''
        if search:
            user = search.group(1)
        for data in datas:
            if data['修改日期'] in (update_date, update_date2, update_date3):
                prompt = data['prompt']
                badcase_output = data['原结果']
                output = data['修改后结果']
                if not prompt or not isinstance(prompt, str) or not output or not isinstance(output, str) or output == '':
                    if not isinstance(prompt, float):
                        info[file]['异常数据'] += 1
                    continue
                if '【对话记录】' not in prompt:
                    continue
                if prompt.startswith('你是一名专业的保险销售员，你的任务是根据【销售链路】【操作流程】【对话记录】判断下一步的【销售流程节点】。') and output not in prompt:
                    error_info_temp = f'<font color="warning">{user}:流程节点输出{output},不在prompt规范中</font>\n'
                    print(error_info_temp)
                    error_info += error_info_temp
                # result, err = check_rule(prompt, output)
                # if not result:
                #     print(err)
                #     error_info_temp = f'<font color="warning">{user}:{err}</font>\n'
                #     if error_info_temp not in error_info:
                #         error_info += error_info_temp
                info[file]['数据量:'] += 1
                date_list.append({
                    'prompt': prompt,
                    'rejected': badcase_output,
                    'chosen': output
                })
        if len(date_list):
            pd.DataFrame(date_list).to_csv(output_dir + update_date_filename + file.replace('-ujd.csv', '.csv'), encoding='utf-8', index=False)

        log = '日期:{},数据量:{},异常量:{}'.format(update_date, info[file]['数据量:'], info[file]['异常数据'])
        print(log)
        if info[file]['数据量:'] > 0:
            warning_info += f'<font color="info">llama3-{user}-{log}</font>\n'
    send_msg(error_info)
    send_msg(warning_info)


if __name__ == '__main__':
    update_date = ''
    if not update_date:
        current_date = datetime.datetime.now()
        update_date = current_date.strftime("%Y/%-m/%-d")
        print(update_date)
    execute('../data/download_csv', '../data/trustable_csv/', update_date)

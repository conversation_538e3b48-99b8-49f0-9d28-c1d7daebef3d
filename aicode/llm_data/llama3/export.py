# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2023/11/23 22:04
import json
import os
import re

import pandas as pd



def get_json_files(directory):
    files = []
    for filename in os.listdir(directory):
        if filename.endswith('.json'):
            files.append(os.path.join(directory, filename))
        elif os.path.isdir(os.path.join(directory, filename)):
            json_files = get_json_files(os.path.join(directory, filename))
            files.extend(json_files)
    return files


def merge_json_files(directory, output_filename):
    data = []
    data2 = []
    files = get_json_files(directory)
    for filename in files:
        with open(filename, 'r', encoding='utf8') as f:
            file_data = json.load(f)
            if isinstance(file_data, list):
                for d in file_data:
                    d['file'] = filename
                data.extend(file_data)
            else:
                print(f"Warning: data in {filename} is not a list and has been skipped.")
    for d in data:
        d['conversations'][0]['value'] = str(d['conversations'][0]['value']).strip()
        if '根据下面提示一步一步执行,不要无中生有。' in d['conversations'][0]['value']:
            task = '话术选择'
        elif '疑问、不满和诉求是什么。' in d['conversations'][0]['value']:
            task = '用户疑义'
        elif '你的任务是根据【销售链路】【操作流程】【对话记录】判断下一步的【销售流程节点】' in d['conversations'][0]['value']:
            task = '流程节点'
        elif '明确同意、倾向同意、疑似同意、中立、倾向拒绝、明确拒绝' in d['conversations'][0]['value']:
            task = '用户意向'
        else:
            task = '其他'
        data_out = {
            '文件名字': d['file'],
            '任务类型': task,
            'prompt': d['conversations'][0]['value'],
            'rejected': d['rejected']['value'],
            'chosen': d['chosen']['value'],
        }
        data2.append(data_out)
    pd.DataFrame(data2).to_csv(f'{output_filename}', index=False, encoding='utf-8')


merge_json_files('./data/trustable_json', './data/output/20250222.csv')

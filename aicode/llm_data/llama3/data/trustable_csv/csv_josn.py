import csv
import json
import os


def csv_to_json(csv_file_path):
    json_items = []
    with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for csv_data in reader:
            # 检查所需的键是否存在
            if 'prompt' in csv_data and 'chosen' in csv_data and 'rejected' in csv_data:
                if str(csv_data['prompt']).strip() != 'nan' and str(csv_data['chosen']).strip() != 'nan' and str(csv_data['rejected']).strip() != 'nan':
                    input_prompt = str(csv_data['prompt']).strip().strip('"').strip()
                    json_item = {
                        "conversations": [
                            {
                                "from": "human",
                                "value": input_prompt
                            }
                        ],
                        "chosen": {
                            "from": "gpt",
                            "value": str(csv_data['chosen']).strip()
                        },
                        "rejected": {
                            "from": "gpt",
                            "value": str(csv_data['rejected']).strip()
                        }
                    }
                    json_items.append(json_item)
    return json_items


def convert_all_csv_files(directory=None):
    # 如果没有指定目录，则使用当前工作目录
    if directory is None:
        directory = os.getcwd()

    # 确保目录路径存在
    if not os.path.exists(directory):
        print(f"目录 {directory} 不存在")
        return

    for filename in os.listdir(directory):
        if filename.endswith('.csv'):
            # 获取CSV文件的完整路径
            csv_file_path = os.path.join(directory, filename)
            # 转换CSV为JSON
            json_items = csv_to_json(csv_file_path)
            # 生成JSON文件名（与CSV文件同名，但扩展名为.json）
            json_filename = os.path.splitext(filename)[0] + '.json'
            # 将JSON文件保存在与CSV文件相同的目录中
            json_file_path = os.path.join(directory, json_filename)
            # 写入JSON文件
            with open(json_file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(json_items, jsonfile, ensure_ascii=False, indent=4)
            print(f"已将 {filename} 转换为 {json_filename} 并保存到 {directory}")


if __name__ == "__main__":
    # 获取当前脚本所在的目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # 将JSON文件保存到脚本所在的目录
    convert_all_csv_files(script_dir)

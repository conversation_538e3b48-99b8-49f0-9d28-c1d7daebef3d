#将指定目录下所有的json文件合并为一个新的json文件，并且新合成的json文件放置在指定的输出目录中
import os
import json
import glob #导入glob模块
def merge_json_files(directory, output_filename):
    data = []
    data2 = []
    files = glob.glob(os.path.join(directory, '*.json')) #使用glob.glob()函数获取目录下的所有.json文件
    print(f"找到以下文件需要合并: {files}")
    for filename in files:
        # 跳过当前脚本文件
        if os.path.basename(filename) == 'json_hb.py':
            continue

        try:
            with open(filename, 'r', encoding='utf8') as f:
                print(f"正在读取文件: {filename}")
                file_data = json.load(f)

                # 处理不同结构的JSON文件
                if isinstance(file_data, list):
                    print(f"  - 文件包含列表数据, 共 {len(file_data)} 项")
                    data.extend(file_data)
                elif isinstance(file_data, dict):
                    print(f"  - 文件包含字典数据")
                    # 将字典添加到列表中
                    data.append(file_data)
                else:
                    print(f"Warning: data in {filename} is not a list or dict and has been skipped.")
        except Exception as e:
            print(f"读取文件 {filename} 时出错: {e}")
    # 处理数据前先检查是否有数据需要处理
    if not data:
        print("警告: 没有找到有效的JSON数据进行合并")
        return

    print(f"开始处理 {len(data)} 条数据")

    for d in data:
        try:
            # 检查数据结构是否符合预期
            if 'conversations' in d and isinstance(d['conversations'], list) and len(d['conversations']) > 0:
                d['conversations'][0]['value'] = str(d['conversations'][0]['value']).strip()

            if 'chosen' in d and isinstance(d['chosen'], dict):
                d['chosen']['value'] = str(d['chosen']['value']).strip()

            if 'rejected' in d and isinstance(d['rejected'], dict):
                d['rejected']['value'] = str(d['rejected']['value']).strip()

            data2.append(d)
        except Exception as e:
            print(f"处理数据时出错: {e}")
            print(f"数据结构: {d}")

    # 检查是否有数据要写入
    if not data2:
        print("警告: 没有有效数据可以写入输出文件")
        return

    # 确保输出目录存在
    output_dir = os.path.dirname(output_filename)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            print(f"创建输出目录: {output_dir}")
        except Exception as e:
            print(f"创建输出目录失败: {e}")
            return

    try:
        with open(output_filename, 'w', encoding='utf8') as f:
            json.dump(data2, f, ensure_ascii=False, indent=4)
            print(f"已将 {directory} 目录下的所有.json文件合并为 {output_filename}")
            print(f"合并了 {len(data2)} 条数据")
    except Exception as e:
        print(f"写入输出文件失败: {e}")

if __name__ == '__main__':
    try:
        # 使用绝对路径指定输入和输出目录
        input_dir = '/Users/<USER>/aicode/llm_data/llama3/data/input'
        output_file = '/Users/<USER>/aicode/llm_data/llama3/data/output/merged.json'

        print(f"开始合并JSON文件...")
        print(f"输入目录: {input_dir}")
        print(f"输出文件: {output_file}")

        merge_json_files(input_dir, output_file)
        print("合并完成!")
    except Exception as e:
        print(f"程序执行过程中出错: {e}")


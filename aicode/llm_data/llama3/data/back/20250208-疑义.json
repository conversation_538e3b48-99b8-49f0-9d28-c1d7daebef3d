[{"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:哎，您好 哎，您终于接电话了，看到您在泰康有个免费意外险已经领取成功了，但是保单一直没有收到，这样日后也就没办法理赔了，来电也是特意提醒您，稍后会把保单通过短信发给您，您注意查收下好吧？\n用户:嗯，不用了，不用了。\n保险规划师:能理解您，不过这个您不用有任何顾虑，这个是完全免费的，保障期一年，到期自动失效，对您没有影响，而且现在意外风险比较高发，多个保障总是没坏处的嘛，您放心就行好吧？\n用户:没有，没有你哪有免费的午餐。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "怀疑诈骗"}, "rejected": {"from": "gpt", "value": "没有免费的午餐"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:就是你说\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:好。\n保险规划师:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，您说\n用户:对。\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:嗯，\n用户:嗯，\n用户:嗯，您说\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“如果有保”，如是假如的如，果是水果的果，有是有没有的有，保是保险的保，如果有保，您输入下，输好之后点下搜索，然后下方会出现如果有保的公众号，看到了吧？\n用户:嗯，您说\n用户:嗯，\n用户:看到。\n保险规划师:然后您点进去，关注下，关注好了之后，在右下角有个我的，您点下，然后第一行有个贵宾通道，看到了吧？\n用户:看到。\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:嗯，\n保险规划师:您输好之后会进入泰康官网页面，泰康惠民健康保，赠全年体检权益，有看到吧\n用户:对。\n用户:嗯，全身体检\n保险规划师:然后您核对下，信息是本人吧？\n用户:您说的全年体检，我没看到。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "没看到体检权益"}, "rejected": {"from": "gpt", "value": "不会查看全年体检"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯嗯。\n保险规划师:您好，您电话总算接通了，恭喜您被评为我们哈啰的优质会员客户，我们限时给您提供了一个医保外的补充医疗，以后医社保报销完，剩余的部分，我们额外再给您报一次，我这边就耽误您几分钟时间给您做个讲解好吧？\n用户:嗯，\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到一条短信，哈啰出行保开头的，您看下收到了吧？\n用户:因为我要收钱吗？\n用户:嗯，然后呢。\n保险规划师:然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着600万医疗保障看到了吧？\n用户:嗯，然后呢。\n保险规划师:然后您核对下，信息是本人吧？\n用户:嗯，然后呢。\n保险规划师:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，进口药自费药也能报，基础版一个月也就几块钱，您看下费用是几块钱吧？？？\n用户:嗯，\n保险规划师:不过不是所有的客户都能参加，对健康有个审核，您点下立即投保，然后健康没问题，您点右下角的我已确认继续，然后打开支付宝，（停顿下）打开了嘛？\n用户:你怎么，你怎么知道我的个人信息的。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "怎么有我身份信息"}, "rejected": {"from": "gpt", "value": "怎么知道个人信息"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯嗯。\n保险规划师:您好，您电话总算接通了，恭喜您被评为我们哈啰的优质会员客户，我们限时给您提供了一个医保外的补充医疗，以后医社保报销完，剩余的部分，我们额外再给您报一次，我这边就耽误您几分钟时间给您做个讲解好吧？\n用户:嗯，\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到一条短信，哈啰出行保开头的，您看下收到了吧？\n用户:因为我要收钱吗？\n用户:嗯，然后呢。\n保险规划师:然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着600万医疗保障看到了吧？\n用户:嗯，然后呢。\n保险规划师:然后您核对下，信息是本人吧？\n用户:嗯，然后呢。\n保险规划师:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，进口药自费药也能报，基础版一个月也就几块钱，您看下费用是几块钱吧？？？\n用户:嗯，\n保险规划师:不过不是所有的客户都能参加，对健康有个审核，您点下立即投保，然后健康没问题，您点右下角的我已确认继续，然后打开支付宝，（停顿下）打开了嘛？\n用户:你怎么，你怎么知道我的个人信息的。\n保险规划师:因为您是我们哈啰的会员客户，您之前自己注册的信息嘛这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础版每个月就几块钱，没有额外费用，您点下立即投保，然后健康没问题，您点右下角的我已确认继续，然后打开支付宝，（停顿下）打开了嘛？\n用户:我是问你的这个电话号码能不能联系到你。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "外显号码可以联系到你们吗"}, "rejected": {"from": "gpt", "value": "电话号码能否联系到保险规划师"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:(用户没说话)\n保险规划师:您好，您终于接电话了，看到您在泰康的保单一直没理赔过，是我们的高优客户，我们限时给您的保单升级成医保外的补充医疗，以后医社保报销完，剩余的部分，我们额外再给您报一次，我这边就耽误您几分钟时间给您做个讲解好吧？\n用户:不是电话好吧。\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到泰康的官方短信，好效保开头的，您看下收到了吧？\n用户:没看到\n保险规划师:您稍等下，可能有些延迟，您不要挂电话，打开免提，应该给您发过去了，好效保开头的，看到了吧？？？\n用户:哦，看到了。\n保险规划师:然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着泰康600万医疗保障看到了吧？\n用户:怎么的，怎么的\n保险规划师:就是带您去领取下您的保单，告诉您怎么去理赔这个是报销医疗费用的，以后不管是意外还是生病只有住院治疗都能报销，医社保报销完剩余的部分除去免赔是100%报销的，不需要自己花钱了，您把链接打开我带您看下保单，（停顿下）链接点开了嘛？有没有看到蓝色背景的600万医疗保障？\n用户:打开了\n用户:看到了\n保险规划师:然后您核对下，信息是本人吧？\n用户:是啊。\n保险规划师:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，进口药自费药也能报，基础版每个月都是1.9元，没有额外费用，您往下滑，看下左下角费用是1.9元吧？\n用户:啊，我看不见。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "没看到具体费用"}, "rejected": {"from": "gpt", "value": "无法看到费用"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:哦。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:打开了\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:看到了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:看到了\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:看到了\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:九二。\n用户:好了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯嗯。\n保险销售员:然后您核对下，信息是本人吧？\n用户:对。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:哦，我不不想买了。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "不需要百万医疗"}, "rejected": {"from": "gpt", "value": "不想买百万医疗"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:哦。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:打开了\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:看到了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:看到了\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:看到了\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:九二。\n用户:好了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯嗯。\n保险销售员:然后您核对下，信息是本人吧？\n用户:对。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:哦，我不不想买了。\n保险销售员:这个就是跟医社保做补充的，医社保报完的剩下的再给您报一次，进口药自费药都能报，最高能报销300万，基础版一个月就几块钱，您放心参加就行好吧？？？\n用户:那个叫什么医保补充是什么东西啊。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "百万医疗是什么"}, "rejected": {"from": "gpt", "value": "百万医疗"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:哦。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:打开了\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:看到了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:看到了\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:看到了\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:九二。\n用户:好了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯嗯。\n保险销售员:然后您核对下，信息是本人吧？\n用户:对。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:哦，我不不想买了。\n保险销售员:这个就是跟医社保做补充的，医社保报完的剩下的再给您报一次，进口药自费药都能报，最高能报销300万，基础版一个月就几块钱，您放心参加就行好吧？？？\n用户:那个叫什么医保补充是什么东西啊。\n保险销售员:就是一个医疗保险，责任范围内，可以把咱们去医院住院治疗的医疗费用100%给您报了这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:行了。\n保险销售员:这个就几块钱没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，然后健康没问题点我已逐页阅读并同意（停顿下）点好了嘛？\n用户:嗯嗯。\n用户:好了。\n保险销售员:然后会到添加银行卡，因为日后理赔都是打到银行卡的，您选个您常用的银行卡，不用输卡号哈，您点那个选择就可以，然后点下一步，点好了吧？\n用户:要填银银行卡吗？\n保险销售员:这个不用输银行卡号，您就选一个您常用的银行卡，点那个选择就可以了，然后点下一步，点好了吧？？？\n用户:等一下，我找一下银行卡。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "稍等一下"}, "rejected": {"from": "gpt", "value": "稍等一下，我去拿银行卡"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:哦。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:打开了\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:看到了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:看到了\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:看到了\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:九二。\n用户:好了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯嗯。\n保险销售员:然后您核对下，信息是本人吧？\n用户:对。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:哦，我不不想买了。\n保险销售员:这个就是跟医社保做补充的，医社保报完的剩下的再给您报一次，进口药自费药都能报，最高能报销300万，基础版一个月就几块钱，您放心参加就行好吧？？？\n用户:那个叫什么医保补充是什么东西啊。\n保险销售员:就是一个医疗保险，责任范围内，可以把咱们去医院住院治疗的医疗费用100%给您报了这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:行了。\n保险销售员:这个就几块钱没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，然后健康没问题点我已逐页阅读并同意（停顿下）点好了嘛？\n用户:嗯嗯。\n用户:好了。\n保险销售员:然后会到添加银行卡，因为日后理赔都是打到银行卡的，您选个您常用的银行卡，不用输卡号哈，您点那个选择就可以，然后点下一步，点好了吧？\n用户:要填银银行卡吗？\n保险销售员:这个不用输银行卡号，您就选一个您常用的银行卡，点那个选择就可以了，然后点下一步，点好了吧？？？\n用户:等一下，我找一下银行卡。\n保险销售员:好的，不着急，您操作下日后理赔款都是直接打到银行卡的，页面中间不是有个免输入卡号添加嘛，不用输卡号，您就选个您常用的银行卡，然后点选择，点下一步，点好了吧？\n用户:等一下啊。\n保险销售员:嗯，不着急，您看下？？？\n用户:我退出了。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "退出了短险页面"}, "rejected": {"from": "gpt", "value": "无"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:哦。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:打开了\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:看到了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:看到了\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:看到了\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:九二。\n用户:好了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯嗯。\n保险销售员:然后您核对下，信息是本人吧？\n用户:对。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:哦，我不不想买了。\n保险销售员:这个就是跟医社保做补充的，医社保报完的剩下的再给您报一次，进口药自费药都能报，最高能报销300万，基础版一个月就几块钱，您放心参加就行好吧？？？\n用户:那个叫什么医保补充是什么东西啊。\n保险销售员:就是一个医疗保险，责任范围内，可以把咱们去医院住院治疗的医疗费用100%给您报了这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:行了。\n保险销售员:这个就几块钱没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，然后健康没问题点我已逐页阅读并同意（停顿下）点好了嘛？\n用户:嗯嗯。\n用户:好了。\n保险销售员:然后会到添加银行卡，因为日后理赔都是打到银行卡的，您选个您常用的银行卡，不用输卡号哈，您点那个选择就可以，然后点下一步，点好了吧？\n用户:要填银银行卡吗？\n保险销售员:这个不用输银行卡号，您就选一个您常用的银行卡，点那个选择就可以了，然后点下一步，点好了吧？？？\n用户:等一下，我找一下银行卡。\n保险销售员:好的，不着急，您操作下日后理赔款都是直接打到银行卡的，页面中间不是有个免输入卡号添加嘛，不用输卡号，您就选个您常用的银行卡，然后点选择，点下一步，点好了吧？\n用户:等一下啊。\n保险销售员:嗯，不着急，您看下？？？\n用户:我退出了。\n保险销售员:没事，那您重新进下，我等您下（停顿下）进去了吧？？？\n用户:啊。\n用户:工号是多少？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "工号多少"}, "rejected": {"from": "gpt", "value": "输入工号"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:哦。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:打开了\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:看到了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:看到了\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:看到了\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:九二。\n用户:好了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯嗯。\n保险销售员:然后您核对下，信息是本人吧？\n用户:对。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:哦，我不不想买了。\n保险销售员:这个就是跟医社保做补充的，医社保报完的剩下的再给您报一次，进口药自费药都能报，最高能报销300万，基础版一个月就几块钱，您放心参加就行好吧？？？\n用户:那个叫什么医保补充是什么东西啊。\n保险销售员:就是一个医疗保险，责任范围内，可以把咱们去医院住院治疗的医疗费用100%给您报了这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:行了。\n保险销售员:这个就几块钱没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，然后健康没问题点我已逐页阅读并同意（停顿下）点好了嘛？\n用户:嗯嗯。\n用户:好了。\n保险销售员:然后会到添加银行卡，因为日后理赔都是打到银行卡的，您选个您常用的银行卡，不用输卡号哈，您点那个选择就可以，然后点下一步，点好了吧？\n用户:要填银银行卡吗？\n保险销售员:这个不用输银行卡号，您就选一个您常用的银行卡，点那个选择就可以了，然后点下一步，点好了吧？？？\n用户:等一下，我找一下银行卡。\n保险销售员:好的，不着急，您操作下日后理赔款都是直接打到银行卡的，页面中间不是有个免输入卡号添加嘛，不用输卡号，您就选个您常用的银行卡，然后点选择，点下一步，点好了吧？\n用户:等一下啊。\n保险销售员:嗯，不着急，您看下？？？\n用户:我退出了。\n保险销售员:没事，那您重新进下，我等您下（停顿下）进去了吧？？？\n用户:啊。\n用户:工号是多少？\n保险销售员:我的工号是 xxxxxx （播报6位数激活码），输入好了吧？？？\n用户:嗯，好\n用户:好了。\n保险销售员:就是右下角有个我的，然后点第一行贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，就能进入产品详情页，看到了吧？\n用户:嗯，\n用户:嗯，嗯嗯。\n保险销售员:您看下，信息是不是您本人，没问题吧？\n用户:九二。\n保险销售员:您有看到人保无忧医疗险，赠全年体检权益，下面有您的个人信息，有看到吗？\n用户:嗯，嗯嗯。\n保险销售员:你看下左下角费用是不是几块钱啊？\n用户:对两块一毛七。\n保险销售员:您看到费用就是您的这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:点了。\n保险销售员:然后会有个健康告知，您往下滑一直滑到最底下，然后点下下面的我已逐页阅读并同意（停顿下）点好了吧？\n用户:他这个每个月\n用户:对。\n保险销售员:这个就几块钱没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，然后健康没问题点我已逐页阅读并同意（停顿下）点好了嘛？\n用户:对呀，投保成功。\n保险销售员:支付好之后点完成，然后您稍等几秒不要动，会审核能不能通过，通过的话会显示恭喜您投保成功，下面有个一家五口照片看到了吧？\n用户:看到了\n保险销售员:嗯，另外现在还可以给您家人申请这个优惠名额，您支付完成之后，下面有“为家人添加一份保障”，您点下，然后填下您家人的姓名和身份证号，同样是几块钱就可以享受600万的医疗保障。这样家人的保障也就都有了。\n用户:嗯，我不需要了。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "不需要百万医疗-家庭单"}, "rejected": {"from": "gpt", "value": "不需要为家人申请百万医疗"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:啊。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:啊。\n用户:打开了\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，好的。\n用户:嗯，\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:知道。\n保险销售员:您需要先关注一下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:嗯，\n用户:看到了\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:看到了\n保险销售员:然后您核对下，信息是本人吧？\n用户:是个人\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:哦，我不要我不要，我不要这个，我不要保险，都没有骗人的。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "怀疑诈骗"}, "rejected": {"from": "gpt", "value": "不需要百万医疗"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:啊。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:啊。\n用户:打开了\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，好的。\n用户:嗯，\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:知道。\n保险销售员:您需要先关注一下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:嗯，\n用户:看到了\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:看到了\n保险销售员:然后您核对下，信息是本人吧？\n用户:是个人\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:哦，我不要我不要，我不要这个，我不要保险，都没有骗人的。\n保险销售员:中国人保是国企，保险都是在国家监管之下的，这个您完全可以放心这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:我那那那我问你，那我问你，我那我问你，我要做手术，他能不能保吧。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "投保之前的能报销吗"}, "rejected": {"from": "gpt", "value": "百万医疗能报销手术费用吗"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:啊。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:啊。\n用户:打开了\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，好的。\n用户:嗯，\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:知道。\n保险销售员:您需要先关注一下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:嗯，\n用户:看到了\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:看到了\n保险销售员:然后您核对下，信息是本人吧？\n用户:是个人\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:哦，我不要我不要，我不要这个，我不要保险，都没有骗人的。\n保险销售员:中国人保是国企，保险都是在国家监管之下的，这个您完全可以放心这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:我那那那我问你，那我问你，我那我问你，我要做手术，他能不能保吧。\n保险销售员:这个因为您还没有参加，之前的没办法报销那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:你这个人吧，具体东西说我听一下吧。\n保险销售员:诶，您请说（加 2 秒空录音）？？？\n用户:你这个是都是保些什么东西。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "百万医疗保障内容"}, "rejected": {"from": "gpt", "value": "百万医疗保内容"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:啊。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:啊。\n用户:打开了\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，好的。\n用户:嗯，\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:知道。\n保险销售员:您需要先关注一下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:嗯，\n用户:看到了\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:看到了\n保险销售员:然后您核对下，信息是本人吧？\n用户:是个人\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:哦，我不要我不要，我不要这个，我不要保险，都没有骗人的。\n保险销售员:中国人保是国企，保险都是在国家监管之下的，这个您完全可以放心这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:我那那那我问你，那我问你，我那我问你，我要做手术，他能不能保吧。\n保险销售员:这个因为您还没有参加，之前的没办法报销那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:你这个人吧，具体东西说我听一下吧。\n保险销售员:诶，您请说（加 2 秒空录音）？？？\n用户:你这个是都是保些什么东西。\n保险销售员:咱们这个保障范围内，疾病，意外全给您包含在内，进口药自费药都能报，最高能报销300万那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:嗯，\n保险销售员:这个就几块钱没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，然后健康没问题点我已逐页阅读并同意（停顿下）点好了嘛？\n用户:你好啊。\n保险销售员:然后会有个健康告知，您往下滑一直滑到最底下，然后点下下面的我已逐页阅读并同意（停顿下）点好了吧？\n用户:我问你，他这个每个月就是四块多钱吗？一块不会不会？这个月十块头四块多钱，下个月优惠什么？一百多两百多，这样的\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "担心乱扣费"}, "rejected": {"from": "gpt", "value": "百万医疗每个月多少钱"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，\n用户:啊，对\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:我有保单。\n保险销售员:跟我们现有的医保商保是做个补充的我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n用户:我忙着其他的呢，现在没有时间。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "在忙"}, "rejected": {"from": "gpt", "value": "稍等一下，我现在有点忙，稍后再操作"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，\n用户:啊，对\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:我有保单。\n保险销售员:跟我们现有的医保商保是做个补充的我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n用户:我忙着其他的呢，现在没有时间。\n保险销售员:这个服务非常重要，就耽误您1分钟我带您去我们微信公众号上看下，您现在把微信打开，我带您去看下 （停顿）微信打开了吧？？？\n用户:不是你说的什么什么意思，这是\n保险销售员:就是带您去我们微信公众号上，看下具体的保障内容，告诉您怎么理赔，您打开下微信，打开微信和我说下（停顿下）微信打开了嘛？？？\n用户:不是不是你给我说保障你们怎么保，保什么，什么保\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "百万医疗保障内容"}, "rejected": {"from": "gpt", "value": "百万医疗保什么"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，\n用户:啊，对\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:我有保单。\n保险销售员:跟我们现有的医保商保是做个补充的我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n用户:我忙着其他的呢，现在没有时间。\n保险销售员:这个服务非常重要，就耽误您1分钟我带您去我们微信公众号上看下，您现在把微信打开，我带您去看下 （停顿）微信打开了吧？？？\n用户:不是你说的什么什么意思，这是\n保险销售员:就是带您去我们微信公众号上，看下具体的保障内容，告诉您怎么理赔，您打开下微信，打开微信和我说下（停顿下）微信打开了嘛？？？\n用户:不是不是你给我说保障你们怎么保，保什么，什么保\n保险销售员:这个特别全面，您就记住，责任范围内，所有的疾病，所有的意外，只要住院治疗，除去免赔额（轻、快带过）都是100%（重音）报销，最高报销300万，只要不是些违法犯罪的就可以我带您到我们微信公众号上看下，您打开免提，回到手机主屏幕，然后打开微信，您打开微信和我说一下，我带您操作看一下 （停顿下）您微信打开了吧？\n用户:你什么？\n用户:嗯，\n用户:嗯，\n用户:嗯，\n用户:啊，你说\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:嗯，\n用户:嗯，\n用户:哦，公众号。\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:没有啊。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "搜索不到公众号"}, "rejected": {"from": "gpt", "value": "没看到公众号"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:我。\n用户:对。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:我这保单，你现在你是干啥的。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "为什么打电话"}, "rejected": {"from": "gpt", "value": "什么公司的"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂，你好。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，好\n用户:你那个医保啊。\n保险销售员:是一个医保外的补充医疗，以后医保报完剩下的可以再报一次，今天就是带您去看下保单告诉您怎么理赔，您把微信打开，我带您去看下（停顿下）微信打开了吧？？？\n用户:那我保了嘛。\n用户:不是我是问你，我这个，我这个我我保了嘛。\n保险销售员:这个您还没有领成功呢，我这边也是带您去看一下您的保障内容和理赔服务，您把微信打开，这边带您去看下，微信打开了吧？？？\n用户:那我不看了。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "不需要百万医疗"}, "rejected": {"from": "gpt", "value": "不想看了"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂，你好。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，好\n用户:你那个医保啊。\n保险销售员:是一个医保外的补充医疗，以后医保报完剩下的可以再报一次，今天就是带您去看下保单告诉您怎么理赔，您把微信打开，我带您去看下（停顿下）微信打开了吧？？？\n用户:那我保了嘛。\n用户:不是我是问你，我这个，我这个我我保了嘛。\n保险销售员:这个您还没有领成功呢，我这边也是带您去看一下您的保障内容和理赔服务，您把微信打开，这边带您去看下，微信打开了吧？？？\n用户:那我不看了。\n保险销售员:您先别着急嘛，先了解下内容，咱们老客户基本都升级完成了，相信您看完一定会非常满意的我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:啊。\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:嗯，你说吧。\n保险销售员:就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:嗯，\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:嗯，\n保险销售员:我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:那。\n保险销售员:然后您核对下，信息是本人吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:啊。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:嗯，\n保险销售员:这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:那我不，我都不想买，好不好。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "不需要百万医疗"}, "rejected": {"from": "gpt", "value": "不想买百万医疗"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:对了。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:啊。\n用户:哦，不知道啊，听不懂。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "听不懂"}, "rejected": {"from": "gpt", "value": "没听懂"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:哎，\n用户:对。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:行。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:好嘞。\n用户:没有呢？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "没打开微信"}, "rejected": {"from": "gpt", "value": "无"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:哎，\n用户:对。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:行。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:好嘞。\n用户:没有呢？\n保险销售员:因为要带您去我们官方公众号上看下保障内容，不影响您日后申请理赔，您电话不用挂，把微信打开，我带您去看下（停顿下）微信打开了吧？？？\n用户:划不开。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "微信打不开"}, "rejected": {"from": "gpt", "value": "手机没操作能力"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:你你。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:哦。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n用户:嗯，\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:嗯，说\n保险销售员:您需要先关注一下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:嗯，\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，\n保险销售员:您好，您是不是没输好啊，我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:好。\n保险销售员:我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:四百五零\n保险销售员:这边看到您还没输好啊，我的工号是xxxxxx（播报4位数激活码），您操作到哪个页面了啊？\n用户:嗯，我才打开微信。\n保险销售员:您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:好好好怎么的。\n保险销售员:就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:我才打开微信呢。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "无"}, "rejected": {"from": "gpt", "value": "还没打开微信"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂，你是哪里啊？\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:啊，不用啊。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "不需要百万医疗"}, "rejected": {"from": "gpt", "value": "不需要"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:喂，你好。\n用户:是啊，怎么说\n保险规划师:您好，我是泰康的客户经理，来电是看到您之前是在我们这边领取过一份意外险，也是怕您不知道怎么去理赔，带您去领取下您的电子保单，看下怎么理赔，您看好吧？您好，我是泰康的客户经理，来电是看到您之前是在我们这边领取过一份意外险，也是怕您不知道怎么去理赔，带您去领取下您的电子保单，看下怎么理赔，您看好吧？\n用户:哎呀，我现在没有时间，下午六点半以后行不行啊。\n保险规划师:这个服务非常重要，耽误您两分钟我快速跟您说下好吧？？？\n用户:啊。\n保险规划师:确保您的权益，我带您到我们微信公众号上看下，您电话不要挂，把微信打开，打开微信和我说一下，我带您操作看一下 （停顿下）您微信打开了吧？\n用户:说吧。\n用户:还没有啊。\n保险规划师:我带您到我们微信公众号上看下，您打开免提，回到手机主屏幕，然后打开微信，您打开微信和我说一下，我带您操作看一下 （停顿下）您微信打开了吧？\n用户:等一分钟啊。\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "稍等一下"}, "rejected": {"from": "gpt", "value": "用户疑义是：用户没有打开微信。"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:不是。\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你找谁呀？\n保险规划师:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:打开了\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:看到了\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“如果有保”，如是假如的如，果是水果的果，有是有没有的有，保是保险的保，如果有保，您输入下，输好之后点下搜索，然后下方会出现如果有保的公众号，看到了吧？\n用户:嗯，\n保险规划师:您关注下“如果有保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:能。\n保险规划师:您需要先关注一下“如果有保”公众号,关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:看到了\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:谢谢了？嗯。\n保险规划师:您好，您是不是没输好啊，我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:听到了\n用户:说好了。\n保险规划师:这边看到您还没输好啊，我的工号是xxxxxx（播报4位数激活码），您操作到哪个页面了啊？\n用户:说过了，\n保险规划师:我的工号是xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:我知道了，我不弄，我听见了，我说过了啊。\n保险规划师:您先别着急嘛，先了解下内容，咱们老客户基本都升级完成了，相信您看完一定会非常满意的，您输入下我的工号xxxx（播报4位数激活码）我带您看下保单，输好了吧？？？\n用户:说过了，\n保险规划师:您有看到“请输入专属工号”下边有四个框框，然后您输入一下我的工号******（播报6位数激活码），输好了没？\n用户:回来还有八十万\n用户:那处理\n保险规划师:抱歉，刚刚没听清，麻烦您再说下？？？\n用户:好了。\n用户:输好了。\n保险规划师:升级之后是一个全险，以后咱们大小疾病大小意外都能保，进口药自费药都能报销，没有药品限制，合同约定花多少报多少，最高能报销600万，还可以限时领取一个免费的全身体检卡，您输入下我的工号xxxx（播报4位数激活码），就能看到泰康惠民健康保“赠全年体检权益”，看到这个页面了吗？\n用户:这处理不了的\n用户:那你一个月的单子给我看一下。\n用户:(用户没说话)\n保险规划师:就是给您带到一个医保外的补充医疗，带您去看下那之所以给您升级这个保障，是考虑到人吃五谷生百病，加上现在的生活环境，吃的用的都不太卫生，难免要跟医院打交道，万一生个大病需要个几万几十万，不仅自己压力很大，同时也会影响家人的正常生活，保障完善之后以后医疗费用咱们就不用担心了，都给您保障到了，您输入下我的工号xxxx（播报4位数激活码），我先带您去看下好吧？\n用户:我。\n用户:九十。\n用户:一九八一\n用户:这个不行。\n保险规划师:我的工号是 xxxxxx （播报6位数激活码），输入好了吧？？？\n用户:没钱。\n用户:你看一下就这样。\n保险规划师:您先别挂电话嘛，这个对您很重要，您先了解下那您输入下我的工号******（播报6位数激活码），输好了吗？\n用户:说好了。\n保险规划师:您好，您是不是没输好啊，我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:输好了。\n保险规划师:您输好之后会进入泰康官网页面，泰康惠民健康保，赠全年体检权益，有看到吧\n用户:看到了\n保险规划师:然后您核对下，信息是本人吧？\n用户:你你。\n保险规划师:您看下，信息是不是您本人，没问题吧？\n用户:是啊。\n用户:不是。\n保险规划师:那可能是系统信息有误，没关系，您可以改成您的信息那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月都是0.6元，没有额外费用，你点下立即投保，点了吧\n用户:怎么改自己的信息？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "怎么修改个人信息"}, "rejected": {"from": "gpt", "value": "如何修改个人信息"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:喂您好，请问是方女士吗？\n用户:你说。\n保险规划师:诶，你好呃，我是泰康的客户经理，来电话呢，是看到您之前在我们这边领取过一份意外险，也是怕您不知道怎么去理赔呀，但您去看一下您的电子保单，看一下怎么理赔，您看好吧。\n用户:啊，你说\n保险规划师:嗯，确保您的权益，我带您去我们微信公众号上看一下，呃，您这打开免提，然后回到手机主屏幕啊对，然后打开微信。嗯，您打开微信了之后跟我说一下，我带您去操作，咱们看一下嗯。打开微信嗯，您微信打开了吧。\n用户:嗯，\n保险规划师:嗯，好的，打开微信之后，您看到微信右上角有一个圆圈加号了吗？嗯，然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字。啊，请问您这边有看到了吗？\n用户:嗯，看到\n保险规划师:嗯，接下来您点一下公众号，这里在搜索栏输入，如果有保呃，如是假如的，如果是水果的，果有是有没有的，有保是保险的，保嗯，如果有保对您输入一下，输入好了之后您点一下搜索，然后在下方呢？会出现？如果有保的公众号啊，看到了吧。\n用户:嗯，看到。\n保险规划师:然后您点进去关注一下，关注好了之后在右下角有个我的啊，您点一下，然后第一行有激活码，这三个字看到了吧。\n用户:看到。\n保险规划师:然后您点一下激活码，然后输入您的专属激活码，七二七三，三二，嗯，您输入一下。嗯，输入好了吧。\n用户:多少。你重新说一遍，多少激活码。\n保险规划师:嗯，就是可以把您的意外险升级成全险以后，疾病意外都保障了，嗯，激活码是七二七，三三，二嗯，您输入一下，输入好之后啊，会进入产品页写着保额六百万报销，无压力确诊就能赔，看到了吧。\n用户:嗯，看到\n保险规划师:嗯，那就没问题，那保单升级之后呢，以后大病小病，大意外，小意外就都给您保齐保全了，进口药，自费药都能报呃，然后这个费用也很轻松，首月几块钱就可以升级完成，嗯，您点一下下面的立即完善，嗯，然后确认一下费用。对您看一下。啊，操作好了吧。\n用户:嗯，可以\n保险规划师:嗯，然后另外还会赠送给您一个住院押金垫付功能，就是说如果以后啊，住院医院让我们交个十万二十万的押金，咱们这个压力比较大的话，就可以给泰康打电话的，我们直接给您垫付了，不用您自己掏钱，嗯，您看这样小还清楚吧。\n用户:嗯，清\n保险规划师:哦，您不要挂电话，马上为您服务。你好，电话转接过来了，您把微信打开了吗？你好。打开之后，上面有一个搜索，你在上面打上，如果有保四个字如果有保，打上，之后点一下关注\n用户:刚刚已经可以\n保险规划师:什么？\n用户:就刚刚已经弄好掉\n保险规划师:关注了是吗？\n用户:嗯，\n保险规划师:关注之后，右下角有一个我的上面有一个啊，您现在在哪个页面啊。\n用户:已经投保成功了。\n保险规划师:你那边有完成吗？你点完成\n用户:完成。\n保险规划师:点了完成之后，就会有一个升级，六百万保额，这个页面您看有吗？嗯，你点升级六百万保额你就投保成功了，过了今晚十二点就生效了。你返回到如果有保，我带你看你的保单号。返回到如果有保点，我的点个人中心点，我的保单返回一下返回了吗？\n用户:没稍。\n保险规划师:以后就是大病小病意外，只要在公立医院住院的，除去一万的免赔，我们都可以给您做到全额报销的，返回到如果有保了吗？\n用户:嗯，返回了。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "无"}, "rejected": {"from": "gpt", "value": "用户疑义是：保险产品的功能和保障范围。"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯，你好，你说\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你这是那个\n保险规划师:就是给您升级成医保外的补充医疗，这边带您去看下保单确保您的权益，我把保单详情发给您了，您看下有没有收到泰康的官方短信，多点保开头的，有您的名字，您看下收到了吧？\n用户:什么意思？电子就是\n用户:哎，我没有你是发的哪个手机号，发我手机号上\n保险规划师:就是发跟您通话的这个手机号上了，刚刚给您发的，如果有保开头的，您看下收到没？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:这个不是那个什么\n保险规划师:是刚刚给您发的，如果有保开头的，有您的名字，您看下收到了吗？？？\n用户:不是我的怎么会点\n用户:哦，我收到了收到，但是但不是我本人。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "短信内姓名不正确"}, "rejected": {"from": "gpt", "value": "用户疑义是：为什么保单短信不是发给用户本人的？"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:喂你好，请问是刘先生吗？\n用户:喂。嗯，\n保险规划师:诶，你好呃，我是泰康的客户经理，来电话呢，是看到您之前在我们这边领取过一份意外险，也是怕您不知道怎么去理赔呀，带您去看一下您的电子保单，看一下怎么理赔，您看好吧。嗯，确保您的权益，我带您去我们微信公众号上看一下，呃，您这打开免提，然后回到手机主屏幕啊对，然后打开微信。\n用户:我。\n保险规划师:嗯，您打开微信了之后跟我说一下，我带您去操作，咱们看一下嗯。\n用户:我，我\n保险规划师:打开微信\n用户:我我领过吗？\n保险规划师:哦，您不要挂电话，马上为您服务。哎，你好，我是泰康的客户经理，这边继续为您服务，您微信打开了吗？\n用户:嗯，是你你这什么什么东西\n保险规划师:啊，这是一个保障啊，带您领取还有一个体检卡，之前能有在咱们这边呢，领取过这个交通意外险，今天呢，带你回顾一下电子保单。\n用户:哦。\n保险规划师:嗯，那你现在把微信打开，右上角有一个搜索，像放大镜一样的图标就是加号，前面的那个圆圈，咱们点进来输入，如果有保四个字，假如的如水果的，果没有的有保护的，保写这四个字，如果有保写完之后呢，你点下搜索下面呢会出现，如果有保的公众号，咱们进行关注。\n用户:嗯，\n保险规划师:啊，你看你那边关注成功了吗？\n用户:然后呢。\n保险规划师:然后呢，你看点进来，右下角有一个我的，你点一下，里边有个激活码，咱们点进来啊，输入六六五四零幺六六五四零幺，这是我的工号啊，因为您后期的家庭保单呢，都是由我负责的，方便您能够随时找到我，您输入进来之后呢？您看一下核对一下您的信息啊。你看一下您的生日第话，姓名都对吗？\n用户:嗯，是对的。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "无"}, "rejected": {"from": "gpt", "value": "输入完激活码后，核对个人信息是否准确。"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:对呀。\n保险规划师:您好，不好意思跟您说晚了，来电是我们整理老客户保单的时候，发现您在泰康有个补充医保的报销权益已经生效了，但是保单还没收到，这样日后就没办法理赔了，今天就是带您去看下保单，以后医保不报的进口药自费药就都能给您报了，100%报销，占用您2分钟宝贵时间我给您讲解下好吧？\n用户:你说吧。\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到泰康的官方短信，好效保开头的，您看下收到了吧？\n用户:嗯，\n用户:我看一下。\n保险规划师:好的，不着急，您操作下您稍等下，可能有些延迟，您不要挂电话，打开免提，应该给您发过去了，好效保开头的，看到了吧？\n用户:没有。\n保险规划师:您稍等下，可能有些延迟，您不要挂电话，打开免提，应该给您发过去了，好效保开头的，看到了吧？？？\n用户:短信吗？\n保险规划师:就是手机上的短信息，刚刚给您发的，好效保开头的，您看下收到没？？？\n用户:短信还是微信。\n用户:没有。\n用户:但是没有\n用户:借不到\n用户:没有啊。\n保险规划师:是不是您屏蔽短信了啊，您看下屏蔽里有没有？？？\n用户:嗯，屏蔽在哪里看，我也不知道。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "不会查看屏蔽短信"}, "rejected": {"from": "gpt", "value": "用户疑义是：如何查看和解除短信屏蔽？"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:什么事？\n保险规划师:您好，我是泰康的客户经理，来电是看到您之前是在我们这边领取过一份意外险，也是怕您不知道怎么去理赔，带您去领取下您的电子保单，看下怎么理赔，您看好吧？\n用户:泰康。\n保险规划师:我是泰康的，来电就是您之前有领取我们一个免费意外险，今天就是带您去领取下您的保单，教您怎么去理赔我带您看下我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:啊。\n用户:啊。\n用户:嗯，\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:啊，好嘞。\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“如果有保”，如是假如的如，果是水果的果，有是有没有的有，保是保险的保，如果有保，您输入下，输好之后点下搜索，然后下方会出现如果有保的公众号，看到了吧？\n用户:(用户没说话)\n保险规划师:您好，我这样讲解您听的清楚吗？\n用户:如果有保，我这\n保险规划师:您搜索一下“如果有保”，如是假如的如，果是水果的果，有是有没有的有，保是保险的保，如果有保，您输入下，输好之后点下搜索，然后下方会出现如果有保的公众号，看到了吧？\n用户:啊，我这不好弄，我这没有网络。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "手机没网络"}, "rejected": {"from": "gpt", "value": "用户疑义是：网络问题"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:嗯，你好哪位。\n保险规划师:您好，不好意思跟您说晚了，来电是我们整理老客户保单的时候，发现您在泰康有个补充医保的报销权益已经生效了，但是保单还没收到，这样日后就没办法理赔了，今天就是带您去看下保单，以后医保不报的进口药自费药就都能给您报了，100%报销，占用您2分钟宝贵时间我给您讲解下好吧？\n用户:啊，你说吧。\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？\n用户:没有。\n保险规划师:您稍等下，可能有些延迟，您不要挂电话，打开免提，应该给您发过去了，好效保开头的，看到了吧？？？\n用户:我这没有？哎。\n保险规划师:是不是您手机拦截了啊，您看下拦截或者未读短信，屏蔽里有没有？？？\n用户:不是你是泰康的，是是哪里的泰康。\n保险规划师:我们在全国各地都有分中心，这个是专门在线渠道的福利活动，线下没有这么便宜的保险您稍等下，可能有些延迟，您不要挂电话，打开免提，应该给您发过去了，好效保开头的，看到了吧？\n用户:不是我\n用户:没有没有，没有，没有了解了，也是\n保险规划师:那这样我带您去微信公众号上看下，您打开免提，然后回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:泰康的\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:泰康保险吗？\n保险规划师:我是泰康合作的健康险管家，咱们本次服务的公众号是健康公社，健康是身体健康的健康，公社是人民公社的公社，健康公社，您输入下，然后下方会出现健康公社的公众号，看到了吧？？？\n用户:嗯，不是你说的是个人保单，是单位保的，这个是\n保险规划师:这个具体信息我这边没办法看到，我先带您去看下您的保单接下来您点一下公众号这里，在搜索栏输入“健康公社”，健康就是身体健康的健康，公社就是人民公社的公社，公平的公，社会的社，健康公社您输入下，输好之后点下搜索，然后下方会出现健康公社的公众号，看到了吧？\n用户:我不需要我不需要，因为我\n保险规划师:这个您别着急拒绝我嘛，这个就是给您的保单做个升级，保障更加全面了，大小疾病大小意外全部保障到了，进口药自费药都能报销，我先带您去看下好吧？？？\n用户:因为这边有给我单独的\n用户:我自己没保，你不要说国家那个保险啊，我自己没保，没出任何保险，都是单位保的。\n保险规划师:您可能是忙忘了，没关系，咱们今天就是对您之前的保单做个升级就是您搜索下“健康公社”，身体健康的健康，人民公社的公社，健康公社您输入下，输好之后点下搜索，然后下方会出现健康公社的公众号，看到了吧？\n用户:我没有，你不要给我讲任何保险，如果交任何保险，我要投诉的啊，我我自己一分钱都没交保险。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "要投诉"}, "rejected": {"from": "gpt", "value": "用户不愿意继续保险规划师的解释，表示自己不需要保险，并且之前没有购买任何保险。"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:哎，您好 哎，您终于接电话了，看到您在泰康有个免费意外险已经领取成功了，但是保单一直没有收到，这样日后也就没办法理赔了，来电也是特意提醒您，稍后会把保单通过短信发给您，您注意查收下好吧？\n用户:喂。\n用户:我。\n保险规划师:这个保障保的是航空意外和铁路意外，保障100万，保障一年，到期自动失效，是完全免费的，稍后会给您发个泰康的官方短信，有具体的保障内容，您记得看下好吧？\n用户:我不知道怎么弄，哎\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "不会操作"}, "rejected": {"from": "gpt", "value": "保单发放情况"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题等。\n沟通媒介：电话\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:啊，是啊，你说\n保险规划师:您好，可算联系上您了，我是惠民保的服务人员，最近很多客户反馈惠民保报销比例太低了，因此我们为老客户推出了100%报销的医疗保障，是补充医社保新农合报销的，以后医社保报完剩下的可以二次报销，今天就是带您去看下保单，告诉您日后怎么理赔您看好吧？\n用户:呃，这样吗？\n保险规划师:麻烦您在说一次？？？\n用户:是。\n用户:这是什么？什么保单？你保单呢？\n保险规划师:就是给您升级成医保外的补充医疗，这边带您去看下保单确保您的权益，我把保单详情发给您了，您看下有没有收到泰康的官方短信，好效保开头的，您看下收到了吧？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？\n用户:我在看呢。\n保险规划师:好的，不着急，您操作下然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着600万医疗保障，惠民健康保看到了吧？\n用户:我这个到到时候我看看一下再说吧。\n保险规划师:这个您自己看可能不太清楚，我要带您看下保单，告诉您怎么理赔，就耽误您2分钟时间，您点开短信，我带您看下这个是报销医疗费用的，以后不管是意外还是生病只有住院治疗都能报销，医社保报销完剩余的社保内用药除去免赔是100%报销的，您把链接打开我带您看下保单，（停顿下）链接点开了嘛？有没有看到600万医疗保障，惠民健康保？\n用户:啊。\n用户:(用户没说话)\n保险规划师:这是一个全险，小到感冒发烧肠炎胃炎，大到癌症肿瘤心脑血管疾病，只要住院治疗都能报销，您链接点开了嘛？我带您去领取下保单\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？\n用户:我现在打开看了。\n保险规划师:然后您核对下，信息是本人吧？\n用户:到时候我再看了解一下就行。\n保险规划师:这个就几块钱我带您完善下就可以了，不用考虑那么多，以后大小疾病大小意外都保了，您点下右下角的立即投保，我带您去看下保单，（停顿下）立即投保点了吧？？？\n用户:啊。\n用户:一年多少钱吗？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "保费一年多少钱"}, "rejected": {"from": "gpt", "value": "投保费用是多少？"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，你好。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n用户:哦说。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:哦。\n用户:没有。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "没打开微信"}, "rejected": {"from": "gpt", "value": "手机没打开微信"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，你好。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n用户:哦说。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:哦。\n用户:没有。\n保险销售员:那您打开下，我带您看下保单，您就是打开免提，然后回到主屏幕，打开微信，打开了和我说下（停顿下）微信打开了嘛？？？\n用户:打开了\n用户:嗯，\n用户:嗯，\n用户:打开了\n用户:打开了\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:啊。\n用户:嗯，\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "无"}, "rejected": {"from": "gpt", "value": "打开公众号"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，你好。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n用户:哦说。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:哦。\n用户:没有。\n保险销售员:那您打开下，我带您看下保单，您就是打开免提，然后回到主屏幕，打开微信，打开了和我说下（停顿下）微信打开了嘛？？？\n用户:打开了\n用户:嗯，\n用户:嗯，\n用户:打开了\n用户:打开了\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:啊。\n用户:嗯，\n用户:嗯，\n用户:嗯，\n用户:看见了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n用户:嗯，\n用户:嗯，\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，\n用户:嗯，\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，\n用户:嗯，\n用户:嗯，\n用户:没有了。\n保险销售员:就在有康保公众号里边，选项我的第一行就可以找到了我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，\n用户:嗯，\n用户:嗯，\n用户:嗯，我完了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯，\n用户:嗯，\n用户:嗯，\n保险销售员:然后您核对下，信息是本人吧？\n用户:我。\n用户:啊。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "无"}, "rejected": {"from": "gpt", "value": "没看到个人信息"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，你好。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n用户:哦说。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:哦。\n用户:没有。\n保险销售员:那您打开下，我带您看下保单，您就是打开免提，然后回到主屏幕，打开微信，打开了和我说下（停顿下）微信打开了嘛？？？\n用户:打开了\n用户:嗯，\n用户:嗯，\n用户:打开了\n用户:打开了\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:啊。\n用户:嗯，\n用户:嗯，\n用户:嗯，\n用户:看见了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n用户:嗯，\n用户:嗯，\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，\n用户:嗯，\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，\n用户:嗯，\n用户:嗯，\n用户:没有了。\n保险销售员:就在有康保公众号里边，选项我的第一行就可以找到了我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，\n用户:嗯，\n用户:嗯，\n用户:嗯，我完了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯，\n用户:嗯，\n用户:嗯，\n保险销售员:然后您核对下，信息是本人吧？\n用户:我。\n用户:啊。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:嗯，\n用户:哦。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "无"}, "rejected": {"from": "gpt", "value": "没问题"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:啊，哪位\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "什么公司的"}, "rejected": {"from": "gpt", "value": "无"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:啊，哪位\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:没有我现在忙呢。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "在忙"}, "rejected": {"from": "gpt", "value": "稍等一下"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:啊\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:我现在没时间\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "在忙"}, "rejected": {"from": "gpt", "value": "没时间"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:啊，哪位\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "什么公司的"}, "rejected": {"from": "gpt", "value": "是先生还是女士"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:啊，哪位\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:没有我现在忙呢。\n保险销售员:确实服务非常重要，就耽误您2分钟时间我带您去我们微信公众号上看下好吧？？？\n用户:我现在没时间\n保险销售员:这个服务非常重要，就耽误您1分钟我带您去我们微信公众号上看下，您现在把微信打开，我带您去看下 （停顿）微信打开了吧？？？\n用户:我现在忙着没现在。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "在忙"}, "rejected": {"from": "gpt", "value": "在操作了"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:什么？\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:哦，我不用了，谢谢啊。\n保险销售员:这个您别着急拒绝我嘛，这个就是给您的保单做个升级，保障更加全面了，大小疾病大小意外全部保障到了，进口药自费药都能报销，我先带您去看下好吧？？？\n用户:哦，不需要不需要，现在我现在有点事哈再见啊。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "不需要百万医疗-在忙"}, "rejected": {"from": "gpt", "value": "稍等一下"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，是啊。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:没有没有，我现在正在忙呢，我现在正在忙呃，等一会儿再搞吧。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "在忙"}, "rejected": {"from": "gpt", "value": "在操作了"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，是啊。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:没有没有，我现在正在忙呢，我现在正在忙呃，等一会儿再搞吧。\n保险销售员:这个服务非常重要，就耽误您1分钟我带您去我们微信公众号上看下，您现在把微信打开，我带您去看下 （停顿）微信打开了吧？？？\n用户:嗯，我的\n用户:不是我\n用户:我现在不看我现在开在开车，不安全的\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "在忙-开车"}, "rejected": {"from": "gpt", "value": "在开车"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:喂，你好呃，你有什么事\n用户:你能简单介绍一下这款医疗保险吗？\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:您继续\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:我得经过记住同意，才可以给你微信号。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "小助手"}, "rejected": {"from": "gpt", "value": "需要同意才能分享微信号"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:对，您是\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:呃，什么东西你再给我说一遍。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "没听懂-来电目的"}, "rejected": {"from": "gpt", "value": "百万医疗内容"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:对，您是\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:呃，什么东西你再给我说一遍。\n保险销售员:就是带您去看下补充医保的电子保单，告诉您日后怎么去理赔，您电话不要挂，打开免提，然后把微信打开，我带您看下（停顿下）微信打开了吧？？？\n用户:哦，去哪看。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "在哪看电子保单"}, "rejected": {"from": "gpt", "value": "为什么打电话"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:对，您是\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:呃，什么东西你再给我说一遍。\n保险销售员:就是带您去看下补充医保的电子保单，告诉您日后怎么去理赔，您电话不要挂，打开免提，然后把微信打开，我带您看下（停顿下）微信打开了吧？？？\n用户:哦，去哪看。\n用户:哦，行\n用户:啊，您说吧。\n保险销售员:就是咱们微信公众号里边就可以的。您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:有。\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:这个是医保吗？还是\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "百万医疗和医保的区别"}, "rejected": {"from": "gpt", "value": "百万医疗是什么"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:对，您是\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:呃，什么东西你再给我说一遍。\n保险销售员:就是带您去看下补充医保的电子保单，告诉您日后怎么去理赔，您电话不要挂，打开免提，然后把微信打开，我带您看下（停顿下）微信打开了吧？？？\n用户:哦，去哪看。\n用户:哦，行\n用户:啊，您说吧。\n保险销售员:就是咱们微信公众号里边就可以的。您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:有。\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:这个是医保吗？还是\n保险销售员:这个就是和国家的医社保还有新农合做补充的，能100%报销就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:等一下网络不好。\n保险销售员:不着急，您看下能不能连个无线您搜索一下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，您输入下，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，有\n用户:嗯，关注公众号吗？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "是要关注公众号吗"}, "rejected": {"from": "gpt", "value": "是否关注公众号"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:对，您是\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:呃，什么东西你再给我说一遍。\n保险销售员:就是带您去看下补充医保的电子保单，告诉您日后怎么去理赔，您电话不要挂，打开免提，然后把微信打开，我带您看下（停顿下）微信打开了吧？？？\n用户:哦，去哪看。\n用户:哦，行\n用户:啊，您说吧。\n保险销售员:就是咱们微信公众号里边就可以的。您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:有。\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:这个是医保吗？还是\n保险销售员:这个就是和国家的医社保还有新农合做补充的，能100%报销就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:等一下网络不好。\n保险销售员:不着急，您看下能不能连个无线您搜索一下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，您输入下，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，有\n用户:嗯，关注公众号吗？\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:看见了\n用户:好了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:行。\n保险销售员:然后您核对下，信息是本人吧？\n用户:是。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:\n保险销售员:您好，能听到嘛？\n用户:能听到\n保险销售员:这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:那我如果不领这个\n用户:啊，如如果我这个不投保，行不行。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "可以不买百万医疗吗"}, "rejected": {"from": "gpt", "value": "不需要百万医疗"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:在哪里？\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你是哪里的？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "什么公司的产品"}, "rejected": {"from": "gpt", "value": "什么公司的"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:在哪里？\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你是哪里的？\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:哪家公司\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "什么公司的产品"}, "rejected": {"from": "gpt", "value": "什么公司的"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:在哪里？\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你是哪里的？\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:哪家公司\n保险销售员:我是中国人保的合作顾问我带您到我们微信公众号上看下，您打开免提，回到手机主屏幕，然后打开微信，您打开微信和我说一下，我带您操作看一下 （停顿下）您微信打开了吧？\n用户:哦哦。\n用户:呃，这个这个我现在比较忙，也不需要\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "不需要百万医疗-在忙"}, "rejected": {"from": "gpt", "value": "在忙"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:在哪里？\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你是哪里的？\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:哪家公司\n保险销售员:我是中国人保的合作顾问我带您到我们微信公众号上看下，您打开免提，回到手机主屏幕，然后打开微信，您打开微信和我说一下，我带您操作看一下 （停顿下）您微信打开了吧？\n用户:哦哦。\n用户:呃，这个这个我现在比较忙，也不需要\n用户:先这样啊。\n保险销售员:这个服务非常重要，就耽误您2分钟时间，带您去看下，您微信打开了嘛？？？\n用户:\n保险销售员:您好，您微信打开了吗，我带您去我们官方公众号上看下保单（停顿下）微信打开了吧？\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，看到了。\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，有了。\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:朋友都有，还有没有的，有\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "有康保怎么拼写"}, "rejected": {"from": "gpt", "value": "有多少人关注了公众号"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:在哪里？\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你是哪里的？\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:哪家公司\n保险销售员:我是中国人保的合作顾问我带您到我们微信公众号上看下，您打开免提，回到手机主屏幕，然后打开微信，您打开微信和我说一下，我带您操作看一下 （停顿下）您微信打开了吧？\n用户:哦哦。\n用户:呃，这个这个我现在比较忙，也不需要\n用户:先这样啊。\n保险销售员:这个服务非常重要，就耽误您2分钟时间，带您去看下，您微信打开了嘛？？？\n用户:\n保险销售员:您好，您微信打开了吗，我带您去我们官方公众号上看下保单（停顿下）微信打开了吧？\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，看到了。\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，有了。\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:朋友都有，还有没有的，有\n保险销售员:就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:嗯，\n用户:有的。\n保险销售员:您需要先关注一下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:看到了\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:好了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯，看见了。\n保险销售员:然后您核对下，信息是本人吧？\n用户:嗯，是的。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:嗯，看到了。\n保险销售员:这个就几块钱没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，然后健康没问题点我已逐页阅读并同意（停顿下）点好了嘛？\n用户:你这个是多长时间呢？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "百万医疗的保障期限"}, "rejected": {"from": "gpt", "value": "百万医疗保障期限"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:哎，\n用户:哦。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n用户:嗯，好行。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n用户:等会看啊。\n保险销售员:好的，不着急，您操作下我带您到我们微信公众号上看下，您打开免提，回到手机主屏幕，然后打开微信，您打开微信和我说一下，我带您操作看一下 （停顿下）您微信打开了吧？\n用户:嗯，打开了。\n用户:嗯嗯，我这个社社保费的\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:添加朋友\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:等会儿啊我，我那个啊。\n保险销售员:嗯，不着急，您看下？？？\n用户:有康保\n保险销售员:有是有没有的有，康是健康的康，保是保险的保，有康保，您输入下，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？？？\n用户:有康保\n用户:嗯，\n用户:等一会儿啊。\n保险销售员:嗯，没事，不着急，我等您一下？？？\n用户:公众号\n保险销售员:就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:啊。\n用户:好了。\n用户:嗯，看见了，有看到啊。\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:点进去。\n用户:贵宾通道嗯，看到了。\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:等会儿啊，别着急啊。\n保险销售员:嗯，不着急（加5s静音）？？？\n用户:我这刚进来\n用户:公众号\n用户:哦，好了。\n用户:专属工号啊。\n保险销售员:我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:啊，好\n用户:把二九七啊。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "无"}, "rejected": {"from": "gpt", "value": "输入工号正确吗"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:哎，\n用户:哦。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n用户:嗯，好行。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n用户:等会看啊。\n保险销售员:好的，不着急，您操作下我带您到我们微信公众号上看下，您打开免提，回到手机主屏幕，然后打开微信，您打开微信和我说一下，我带您操作看一下 （停顿下）您微信打开了吧？\n用户:嗯，打开了。\n用户:嗯嗯，我这个社社保费的\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:添加朋友\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:等会儿啊我，我那个啊。\n保险销售员:嗯，不着急，您看下？？？\n用户:有康保\n保险销售员:有是有没有的有，康是健康的康，保是保险的保，有康保，您输入下，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？？？\n用户:有康保\n用户:嗯，\n用户:等一会儿啊。\n保险销售员:嗯，没事，不着急，我等您一下？？？\n用户:公众号\n保险销售员:就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:啊。\n用户:好了。\n用户:嗯，看见了，有看到啊。\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:点进去。\n用户:贵宾通道嗯，看到了。\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:等会儿啊，别着急啊。\n保险销售员:嗯，不着急（加5s静音）？？？\n用户:我这刚进来\n用户:公众号\n用户:哦，好了。\n用户:专属工号啊。\n保险销售员:我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:啊，好\n用户:把二九七啊。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯，看到了。\n保险销售员:然后您核对下，信息是本人吧？\n用户:啊。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:你。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "无"}, "rejected": {"from": "gpt", "value": "什么公司的产品"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:哎，\n用户:哦。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n用户:嗯，好行。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n用户:等会看啊。\n保险销售员:好的，不着急，您操作下我带您到我们微信公众号上看下，您打开免提，回到手机主屏幕，然后打开微信，您打开微信和我说一下，我带您操作看一下 （停顿下）您微信打开了吧？\n用户:嗯，打开了。\n用户:嗯嗯，我这个社社保费的\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:添加朋友\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:等会儿啊我，我那个啊。\n保险销售员:嗯，不着急，您看下？？？\n用户:有康保\n保险销售员:有是有没有的有，康是健康的康，保是保险的保，有康保，您输入下，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？？？\n用户:有康保\n用户:嗯，\n用户:等一会儿啊。\n保险销售员:嗯，没事，不着急，我等您一下？？？\n用户:公众号\n保险销售员:就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:啊。\n用户:好了。\n用户:嗯，看见了，有看到啊。\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:点进去。\n用户:贵宾通道嗯，看到了。\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:等会儿啊，别着急啊。\n保险销售员:嗯，不着急（加5s静音）？？？\n用户:我这刚进来\n用户:公众号\n用户:哦，好了。\n用户:专属工号啊。\n保险销售员:我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:啊，好\n用户:把二九七啊。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯，看到了。\n保险销售员:然后您核对下，信息是本人吧？\n用户:啊。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:你。\n用户:这一天多少钱呢？这交一个月交多少钱。\n保险销售员:这个基础版每个月都是几块钱，费用不变这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:啊。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:哦。\n保险销售员:这个就几块钱没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，然后健康没问题点我已逐页阅读并同意（停顿下）点好了嘛？\n用户:一月五块四啊。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "核对百万医疗费用"}, "rejected": {"from": "gpt", "value": "无"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:嗯，\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:是的。\n用户:嗯，\n用户:那个医保是不是\n保险销售员:是一个医保外的补充医疗，以后医保报完剩下的可以再报一次，今天就是带您去看下保单告诉您怎么理赔，您把微信打开，我带您去看下（停顿下）微信打开了吧？？？\n用户:我这我，我我自己的微信啊。\n保险销售员:确保您的权益，我带您到我们微信公众号上看下，您电话不要挂，把微信打开，打开微信和我说一下，我带您操作看一下 （停顿下）您微信打开了吧？\n用户:哦，我\n用户:呃，我返回一下我的，哎我，我打开\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:等等一下，我打开看看，我都不会弄。\n保险销售员:这个操作挺简单的，您跟着我一步一步操作就行您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:微信点我的吧。\n保险销售员:就是在微信右上角有个带圆圈的➕号，您点下，然后再点第二行的添加朋友，在倒数第二行有公众号这三个字，看到了吧？\n用户:我微信我，这我这个微信我都没打开嘞。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，好我，我交交，交交，同同事把它打开，我微信我都\n保险销售员:您好有看到公众号三个字嘛？就是您点下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，看到了吧？\n用户:等一下啊。\n保险销售员:嗯，不着急，您看下？？？\n用户:\n保险销售员:您好，能听到嘛？\n用户:嗯，听得到\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:叫我感觉是微信怎么的。\n保险销售员:就是带您去我们公众号上，看下您之前领取的保单您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:那你微信\n用户:对对。\n用户:没有添加朋友。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "没看见添加朋友的选项"}, "rejected": {"from": "gpt", "value": "没有添加朋友"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:谁呀？\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，那好吧，谢谢。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n用户:嗯，还在找嘞。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "在操作了"}, "rejected": {"from": "gpt", "value": "稍等一下"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:谁呀？\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，那好吧，谢谢。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n用户:嗯，还在找嘞。\n保险销售员:好的，不着急，您操作下您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "无"}, "rejected": {"from": "gpt", "value": "没看到具体公众号"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:是啊。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:啊，可以\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n用户:\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:看到了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，看到了。\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:啊，还没有。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "还没有输入工号"}, "rejected": {"from": "gpt", "value": "输入工号"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:什么电子保单呢？就是\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "什么电子保单"}, "rejected": {"from": "gpt", "value": "不知道什么是电子保单"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:你是哪里？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "什么公司的"}, "rejected": {"from": "gpt", "value": "不知道销售员的位置"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "无"}, "rejected": {"from": "gpt", "value": "没接听"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:你是哪里？\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你说吧。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，打开了。\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:看到了，你说\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:看到了\n用户:嗯，\n用户:嗯，\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:泰康的是吧。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "什么公司的产品"}, "rejected": {"from": "gpt", "value": "泰康的是吧"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:你是哪里？\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你说吧。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，打开了。\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:看到了，你说\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:看到了\n用户:嗯，\n用户:嗯，\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:泰康的是吧。\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡然后您核对下，信息是本人吧？\n用户:嗯，\n保险销售员:这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:你给我最好的时候讲完他这个的是啊，什么费用立即投保，这个是不是又又又买的？这个叫什么保险？一年费率多少。\n保险销售员:就是一个医疗保险，责任范围内，可以把咱们去医院住院治疗的医疗费用100%给您报了那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:一个月多少钱。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "百万医疗每个月多少钱"}, "rejected": {"from": "gpt", "value": "保费一年多少钱"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:你是哪里？\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你说吧。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，打开了。\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:看到了，你说\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:看到了\n用户:嗯，\n用户:嗯，\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:泰康的是吧。\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡然后您核对下，信息是本人吧？\n用户:嗯，\n保险销售员:这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:你给我最好的时候讲完他这个的是啊，什么费用立即投保，这个是不是又又又买的？这个叫什么保险？一年费率多少。\n保险销售员:就是一个医疗保险，责任范围内，可以把咱们去医院住院治疗的医疗费用100%给您报了那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:一个月多少钱。\n保险销售员:这个基础版每个月都是几块钱，费用不变这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:我不用了，我自己就那个医保好了。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "不需要百万医疗-有社保"}, "rejected": {"from": "gpt", "value": "不需要额外的保险"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:你是哪里？\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你说吧。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，打开了。\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:看到了，你说\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:看到了\n用户:嗯，\n用户:嗯，\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:泰康的是吧。\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡然后您核对下，信息是本人吧？\n用户:嗯，\n保险销售员:这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:你给我最好的时候讲完他这个的是啊，什么费用立即投保，这个是不是又又又买的？这个叫什么保险？一年费率多少。\n保险销售员:就是一个医疗保险，责任范围内，可以把咱们去医院住院治疗的医疗费用100%给您报了那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:一个月多少钱。\n保险销售员:这个基础版每个月都是几块钱，费用不变这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:我不用了，我自己就那个医保好了。\n保险销售员:那很好呀，给您带到的百万医疗是跟医保做个互相补充，把医保不报的都保进来了，进口药自费药都能报，就是做个升级，费用就几块钱那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:嗯，\n用户:\n保险销售员:这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:嗯，\n用户:嗯，点了。\n保险销售员:这个就几块钱没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，然后健康没问题点我已逐页阅读并同意（停顿下）点好了嘛？\n用户:嗯，好了。\n保险销售员:然后会到添加银行卡，因为日后理赔都是打到银行卡的，您选个您常用的银行卡，不用输卡号哈，您点那个选择就可以，然后点下一步，点好了吧？\n用户:嗯，\n用户:嗯，\n用户:利息签约京东支付啊。\n保险销售员:您就选个您常用的银行就行，因为日后理赔钱都直接打到银行卡里，您点下那个选择，然后点下一步，点好了吧？？？\n用户:嗯，\n用户:嗯，立即签约，嗯\n保险销售员:然后会验证下您的手机号，您点下获取验证码，会给您发个6位数的验证码，然后您填下，（停顿2s）填好了吧？\n用户:两块九毛五。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "无"}, "rejected": {"from": "gpt", "value": "没看到具体的金额"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:啊，你说\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:什么意思？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "没听懂-来电目的"}, "rejected": {"from": "gpt", "value": "不清楚具体含义"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:啊，你说\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:什么意思？\n保险销售员:就是带您去领取下您的保单，教您怎么去理赔，我带您去微信公众号上看下，您电话不要挂，打开免提，然后把微信打开，（停顿下）微信打开了吧？？？\n用户:什么意思？\n用户:你是哪里？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "什么公司的产品"}, "rejected": {"from": "gpt", "value": "不清楚身份"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:啊，你说\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:什么意思？\n保险销售员:就是带您去领取下您的保单，教您怎么去理赔，我带您去微信公众号上看下，您电话不要挂，打开免提，然后把微信打开，（停顿下）微信打开了吧？？？\n用户:什么意思？\n用户:你是哪里？\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:不是我没有买呀，我没买你们保险啊。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "没买过百万医疗"}, "rejected": {"from": "gpt", "value": "没有购买保险"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:啊，你说\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:什么意思？\n保险销售员:就是带您去领取下您的保单，教您怎么去理赔，我带您去微信公众号上看下，您电话不要挂，打开免提，然后把微信打开，（停顿下）微信打开了吧？？？\n用户:什么意思？\n用户:你是哪里？\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:不是我没有买呀，我没买你们保险啊。\n保险销售员:这个您还没有领成功呢，我这边也是带您去看一下您的保障内容和理赔服务，您把微信打开，这边带您去看下，微信打开了吧？？？\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "无"}, "rejected": {"from": "gpt", "value": "没听到"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:啊，你说\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:什么意思？\n保险销售员:就是带您去领取下您的保单，教您怎么去理赔，我带您去微信公众号上看下，您电话不要挂，打开免提，然后把微信打开，（停顿下）微信打开了吧？？？\n用户:什么意思？\n用户:你是哪里？\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:不是我没有买呀，我没买你们保险啊。\n保险销售员:这个您还没有领成功呢，我这边也是带您去看一下您的保障内容和理赔服务，您把微信打开，这边带您去看下，微信打开了吧？？？\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:你说呢。\n用户:你说。\n保险销售员:您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:你这个是哪个公众号啊？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "什么公众号"}, "rejected": {"from": "gpt", "value": "公众号名称"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:啊，你说\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:什么意思？\n保险销售员:就是带您去领取下您的保单，教您怎么去理赔，我带您去微信公众号上看下，您电话不要挂，打开免提，然后把微信打开，（停顿下）微信打开了吧？？？\n用户:什么意思？\n用户:你是哪里？\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:不是我没有买呀，我没买你们保险啊。\n保险销售员:这个您还没有领成功呢，我这边也是带您去看一下您的保障内容和理赔服务，您把微信打开，这边带您去看下，微信打开了吧？？？\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:你说呢。\n用户:你说。\n保险销售员:您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:你这个是哪个公众号啊？\n保险销售员:咱们专属公众号是“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，您输入下，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？？？\n用户:然后呢。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "然后怎么操作"}, "rejected": {"from": "gpt", "value": "公众号搜索"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:啊，你说\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:什么意思？\n保险销售员:就是带您去领取下您的保单，教您怎么去理赔，我带您去微信公众号上看下，您电话不要挂，打开免提，然后把微信打开，（停顿下）微信打开了吧？？？\n用户:什么意思？\n用户:你是哪里？\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:不是我没有买呀，我没买你们保险啊。\n保险销售员:这个您还没有领成功呢，我这边也是带您去看一下您的保障内容和理赔服务，您把微信打开，这边带您去看下，微信打开了吧？？？\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:你说呢。\n用户:你说。\n保险销售员:您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:你这个是哪个公众号啊？\n保险销售员:咱们专属公众号是“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，您输入下，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？？？\n用户:然后呢。\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，然后呢。\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:你等一下啊。\n保险销售员:我的工号是 xxxxxx （播报6位数激活码），输入好了吧？？？\n用户:要先关注是吧。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "是要关注公众号吗"}, "rejected": {"from": "gpt", "value": "输入工号"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:啊，你说\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:什么意思？\n保险销售员:就是带您去领取下您的保单，教您怎么去理赔，我带您去微信公众号上看下，您电话不要挂，打开免提，然后把微信打开，（停顿下）微信打开了吧？？？\n用户:什么意思？\n用户:你是哪里？\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:不是我没有买呀，我没买你们保险啊。\n保险销售员:这个您还没有领成功呢，我这边也是带您去看一下您的保障内容和理赔服务，您把微信打开，这边带您去看下，微信打开了吧？？？\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:你说呢。\n用户:你说。\n保险销售员:您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:你这个是哪个公众号啊？\n保险销售员:咱们专属公众号是“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，您输入下，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？？？\n用户:然后呢。\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，然后呢。\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:你等一下啊。\n保险销售员:我的工号是 xxxxxx （播报6位数激活码），输入好了吧？？？\n用户:要先关注是吧。\n用户:要先关注是吧。\n保险销售员:您需要先关注一下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:\n保险销售员:您好，能听到嘛？\n用户:他这个有空要先要要能够登录，要先登录进去的是吧。\n保险销售员:这个不用登陆，您就输入我的工号就可以了然后您点进去，关注下，关注好了之后，在右下角有个我的，您点下，然后第一行有个贵宾通道，看到了吧？\n用户:我没有没有进到你这个页面。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "页面打不开"}, "rejected": {"from": "gpt", "value": "没进入页面"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:啊，你说\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:什么意思？\n保险销售员:就是带您去领取下您的保单，教您怎么去理赔，我带您去微信公众号上看下，您电话不要挂，打开免提，然后把微信打开，（停顿下）微信打开了吧？？？\n用户:什么意思？\n用户:你是哪里？\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:不是我没有买呀，我没买你们保险啊。\n保险销售员:这个您还没有领成功呢，我这边也是带您去看一下您的保障内容和理赔服务，您把微信打开，这边带您去看下，微信打开了吧？？？\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:\n保险销售员:您好，能听到嘛？\n用户:你说呢。\n用户:你说。\n保险销售员:您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:你这个是哪个公众号啊？\n保险销售员:咱们专属公众号是“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，您输入下，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？？？\n用户:然后呢。\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，然后呢。\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:你等一下啊。\n保险销售员:我的工号是 xxxxxx （播报6位数激活码），输入好了吧？？？\n用户:要先关注是吧。\n用户:要先关注是吧。\n保险销售员:您需要先关注一下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:\n保险销售员:您好，能听到嘛？\n用户:他这个有空要先要要能够登录，要先登录进去的是吧。\n保险销售员:这个不用登陆，您就输入我的工号就可以了然后您点进去，关注下，关注好了之后，在右下角有个我的，您点下，然后第一行有个贵宾通道，看到了吧？\n用户:我没有没有进到你这个页面。\n用户:不是有空是个对话电话的那个呀。\n保险销售员:这个您可以放心，这个是我们这次活动的通知电话，今天就是带您去看下您之前的保单就是右下角有个我的，点下，然后第一行是贵宾通道，然后会出现请输入专属工号看到了吧？\n用户:是不是进入什么个人中心啊。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "点击什么菜单"}, "rejected": {"from": "gpt", "value": "进入个人中心"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:是啊。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:什么东西？\n保险销售员:是一个医保外的补充医疗，以后医保报完剩下的可以再报一次，今天就是带您去看下保单告诉您怎么理赔，您把微信打开，我带您去看下（停顿下）微信打开了吧？？？\n用户:啊。\n用户:嗯，\n用户:家里。\n用户:加了你发过来嘛。\n用户:啊。\n保险销售员:这个您自己看可能不太清楚，我就耽误您两分钟时间带您去我们官方公众号上看下您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:没有。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "没看见添加朋友的选项"}, "rejected": {"from": "gpt", "value": "没看到公众号"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:对。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，你们是什么公司呀。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "什么公司的产品"}, "rejected": {"from": "gpt", "value": "公司名称"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:对。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，你们是什么公司呀。\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，打开了。\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:嗯，\n用户:打开了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:有康保是中国人保旗下的吗？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "有康保是什么"}, "rejected": {"from": "gpt", "value": "公众号是否是中国人保旗下"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:对。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，你们是什么公司呀。\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，打开了。\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:嗯，\n用户:打开了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:有康保是中国人保旗下的吗？\n保险销售员:这个公众号就是专门服务老用户的，您放心。就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:哦，看到了。\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:这是一个中介公司吧。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "你们是保险中介公司吗"}, "rejected": {"from": "gpt", "value": "怀疑是中介公司"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:对。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，你们是什么公司呀。\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，打开了。\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:嗯，\n用户:打开了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:有康保是中国人保旗下的吗？\n保险销售员:这个公众号就是专门服务老用户的，您放心。就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:哦，看到了。\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:这是一个中介公司吧。\n保险销售员:这个公众号就是专门服务老用户的，您放心。您需要先关注一下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:点了。\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:嗯，输入了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯，\n保险销售员:然后您核对下，信息是本人吧？\n用户:对。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:报销范围是什么？是超过社保范围还是同社保的范围？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "百万医疗报销范围"}, "rejected": {"from": "gpt", "value": "报销范围"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:对。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，你们是什么公司呀。\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，打开了。\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:嗯，\n用户:打开了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:有康保是中国人保旗下的吗？\n保险销售员:这个公众号就是专门服务老用户的，您放心。就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:哦，看到了。\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:这是一个中介公司吧。\n保险销售员:这个公众号就是专门服务老用户的，您放心。您需要先关注一下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:点了。\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:嗯，输入了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯，\n保险销售员:然后您核对下，信息是本人吧？\n用户:对。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:报销范围是什么？是超过社保范围还是同社保的范围？\n保险销售员:就是保障范围内大小疾病大小意外只要住院治疗就能报销，一般疾病有1万的免赔额，重大疾病0免赔。这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:哦。\n用户:报销范围能跟我讲一下吗？报销范围\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "百万医疗报销范围"}, "rejected": {"from": "gpt", "value": "报销范围"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:对。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，你们是什么公司呀。\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，打开了。\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:嗯，\n用户:打开了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:有康保是中国人保旗下的吗？\n保险销售员:这个公众号就是专门服务老用户的，您放心。就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:哦，看到了。\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:这是一个中介公司吧。\n保险销售员:这个公众号就是专门服务老用户的，您放心。您需要先关注一下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:点了。\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:\n保险销售员:您好，能听到嘛？\n用户:嗯，输入了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯，\n保险销售员:然后您核对下，信息是本人吧？\n用户:对。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:报销范围是什么？是超过社保范围还是同社保的范围？\n保险销售员:就是保障范围内大小疾病大小意外只要住院治疗就能报销，一般疾病有1万的免赔额，重大疾病0免赔。这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础班每个月就几块钱，没有额外费用，您点下立即投保，点了吗？\n用户:哦。\n用户:报销范围能跟我讲一下吗？报销范围\n保险销售员:就是保障范围内大小疾病大小意外只要住院治疗就能报销，一般疾病有1万的免赔额，重大疾病0免赔。那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:保障范围是什么？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "百万医疗保障内容"}, "rejected": {"from": "gpt", "value": "保障范围"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂，你好，哪位\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你是哪个银行啊？\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n用户:嗯，\n用户:打开。\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:嗯，\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:看到了\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:啊。\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯嗯。\n用户:\n保险销售员:您好，能听到嘛？\n用户:能听到\n保险销售员:我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:多了。\n用户:嗯，\n保险销售员:您有看到“请输入专属工号”下边有四个框框，然后您输入一下我的工号******（播报4位数激活码），输好了没？\n用户:做好了，说好了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯，\n保险销售员:然后您核对下，信息是本人吧？\n用户:对。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:嗯，\n用户:嗯，\n保险销售员:这个就几块钱没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，然后健康没问题点我已逐页阅读并同意（停顿下）点好了嘛？\n用户:你你这个\n保险销售员:然后会有个健康告知，您往下滑一直滑到最底下，然后点下下面的我已逐页阅读并同意（停顿下）点好了吧？\n用户:你这个是人保吗？\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "什么公司的产品"}, "rejected": {"from": "gpt", "value": "怀疑是假冒人保"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:喂，你好，哪位\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你是哪个银行啊？\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n用户:嗯，\n用户:打开。\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:嗯，\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:看到了\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:啊。\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯嗯。\n用户:\n保险销售员:您好，能听到嘛？\n用户:能听到\n保险销售员:我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:多了。\n用户:嗯，\n保险销售员:您有看到“请输入专属工号”下边有四个框框，然后您输入一下我的工号******（播报4位数激活码），输好了没？\n用户:做好了，说好了。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:嗯，\n保险销售员:然后您核对下，信息是本人吧？\n用户:对。\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:嗯，\n用户:嗯，\n保险销售员:这个就几块钱没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，然后健康没问题点我已逐页阅读并同意（停顿下）点好了嘛？\n用户:你你这个\n保险销售员:然后会有个健康告知，您往下滑一直滑到最底下，然后点下下面的我已逐页阅读并同意（停顿下）点好了吧？\n用户:你这个是人保吗？\n用户:我知道我知道我说你这个是\n用户:人民保险嘛。\n保险销售员:这个是由中国人民保险给您承保的，专门针对国家的医社保做的补充，以后医社保不报的进口药自费药都可以给您报了这个就几块钱没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，然后健康没问题点我已逐页阅读并同意（停顿下）点好了嘛？\n用户:我知道我知道到到时候我再弄吧，我现在有事啊。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "考虑一下再买百万医疗"}, "rejected": {"from": "gpt", "value": "暂时不处理"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据【销售链路】和【对话记录】判断【用户疑义】。\n\n用户疑义的定义：在销售保险过程中，用户的疑问、不满和诉求，包括对保险公司的疑问、对保险产品的疑问、用户对自身情况和适用性的疑问、操作相关的疑问、信任和投诉相关问题\n等。\n沟通媒介：电话\n销售链路：通过微信公众号打开页面入口，在页面输入工号跳转到带有用户姓名、身份证的投保页面，引导用户投保百万医疗保险并通过银行卡支付保费。\n\n公众号名称：有康保\n保险产品类型：百万医疗\n支付方式：银行卡\n\n要求：\n1、结合对话内容只识别用户最后一轮对话的用户疑义，忽略最后一轮对话之前的用户疑义，不要无中生有。如果最后一轮用户连续说了多句话，那么综合考虑多句话的用户疑义。\n2、对话记录中，用户说的话是通过语音识别的，可能存在谐音字、方言，有时需要根据对话上文进行合理推测。\n3、用户疑义通常是用户视角的，如果无法从用户视角进行总结，可以从销售者视角进行总结。用户疑义尽可能简短。\n4、如果用户当前没有问题，则输出“无”。\n5、当用户有多个疑义时，以销售为目的总结出最重要的一个用户疑义。\n6、产品相关问题，要结合销售链路中的产品类型和具体的对话上下文判断用户疑义。\n\n对话记录：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:不是。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你这是哪里呀？\n保险销售员:我是中国人保的合作顾问，咱们今天就是带您去看下补充医保的保单，以后医保报完再给您报一次，同时给您赠送了一个免费的全身体检卡我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:没有你说\n保险销售员:那您打开下，我带您看下保单，您就是打开免提，然后回到主屏幕，打开微信，打开了和我说下（停顿下）微信打开了嘛？？？\n用户:哪里的保单呀？\n保险销售员:就是给您升级成医保外的补充医疗，这边带您去看下保单您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:我不想参加\n保险销售员:这个您别着急拒绝我嘛，这个就是给您的保单做个升级，保障更加全面了，大小疾病大小意外全部保障到了，进口药自费药都能报销，我先带您去看下好吧？？？\n用户:你说。\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:没有。\n保险销售员:您应该是没点公众号，您先点旁边的取消，然后回到添加朋友的页面，然后在最下面一行是企业微信联系人，倒数第二行是公众号，您点下，然后再输有康保，就能搜到了，（停顿下）看到了吧？？？\n用户:没有。\n保险销售员:这样您都退出来，回到微信聊天的页面，然后点上面的放大镜搜索，然后输入有康保，搜索，然后下面第一个多点保您点下，就能看到有康保公众号，（停顿下）看到了吧？？？\n用户:这个医保有啥关系了呀。\n\n```\n\n用户疑义是："}], "chosen": {"from": "gpt", "value": "百万医疗和医保的区别"}, "rejected": {"from": "gpt", "value": "对医保的作用不清楚"}}]
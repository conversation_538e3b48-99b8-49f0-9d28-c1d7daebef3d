[{"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据对话记录收集用户情境。对话记录是通过电话向用户销售保险的对话记录，用户情境包括用户的个人的工作、婚姻状态、财务状况、家庭成员情况以及保险情况。\n\n注意：\n1、对话记录是保险规划师通过电话跟用户沟通的对话记录，对话记录的内容是通过语音识别出来的，可能存在错别字、谐音字。\n2、只需收集用户最后一轮对话里包含的情境，并且只输出最后一轮对话里包含的情境。\n3、输出格式为“职业:具体职业”。如果收集到多个用户情境(例如职业和婚姻)则用\\n分割，例如“职业:具体职业\\n婚姻:具体婚姻状态”。其中用户情境包括:职业、婚姻、孩子数量、孩子阶段。无需输出未提及的用户情境。没有任何用户情境则输出“无”。\n4、如果用户说了多次同一个用户情境，需要总结成一个结果。前后不一致时，以后面为准。\n5、用户的职业除了常见职业外，还包括“家庭主妇”、“无业”、“自由职业”、“退休或老年人”。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯\n保险规划师:您好，您终于接电话了，看到您在泰康的保单一直没理赔过，是我们的高优客户，我们限时给您的保单升级成医保外的补充医疗，以后医社保报销完，剩余的部分，我们额外再给您报一次，我这边就耽误您几分钟时间给您做个讲解好吧？\n用户:好\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到泰康的官方短信，好效保开头的，您看下收到了吧？\n用户:收到了\n保险规划师:然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着泰康600万医疗保障看到了吧？\n用户:嗯\n保险规划师:然后您核对下，信息是本人吧？\n用户:是\n保险规划师:这次机会确实非常难得，是咱们专门针对医社保做的补充，大小疾病大小意外都能保，进口药自费药都能报销，没有药品限制，合同约定花多少报多少，最高能报销600万，这样讲还好理解吧？\n用户:好的\n保险规划师:这个不是所有人都能参与，对职业和健康有一定要求，请问您是做什么职业的?\n用户:问这个干什么\n保险规划师:因为对职业有要求，怕影响您之后理赔，所以需要问您是做什么工作的？？？\n用户:那不用了\n保险规划师:您先别着急拒绝我嘛，今天给您带到的服务特别实用，小到生活当中有个肠炎胃炎，头疼脑热，大到癌症肿瘤，都给您保齐保全了，以后您就不用担心医疗费的问题了，不过不是所有职业都能报销，所以需要问下您的职业是啥？？？\n用户:我是做生意的，我先考虑下再说吧\n保险规划师:这个保险非常全面，大病小病意外，只要住院产生的费用医保不给报销的，除去免赔之后都能给咱们报销了，这样住院看病就不用花自己的钱了。非常合适老客户都参加了，您是做什么生意的，开工厂、饭店还是其他的啊？\n用户:小饭馆\n```\n\n最后一轮对话中的用户情境是："}], "chosen": {"from": "gpt", "value": "职业:小饭馆老板"}, "rejected": {"from": "gpt", "value": "职业:做生意\n生意类型:小饭馆"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据对话记录收集用户情境。对话记录是通过电话向用户销售保险的对话记录，用户情境包括用户的个人的工作、婚姻状态、财务状况、家庭成员情况以及保险情况。\n\n注意：\n1、对话记录是保险规划师通过电话跟用户沟通的对话记录，对话记录的内容是通过语音识别出来的，可能存在错别字、谐音字。\n2、只需收集用户最后一轮对话里包含的情境，并且只输出最后一轮对话里包含的情境。\n3、输出格式为“职业:具体职业”。如果收集到多个用户情境(例如职业和婚姻)则用\\n分割，例如“职业:具体职业\\n婚姻:具体婚姻状态”。其中用户情境包括:职业、婚姻、孩子数量、孩子阶段。无需输出未提及的用户情境。没有任何用户情境则输出“无”。\n4、如果用户说了多次同一个用户情境，需要总结成一个结果。前后不一致时，以后面为准。\n5、用户的职业除了常见职业外，还包括“家庭主妇”、“无业”、“自由职业”、“退休或老年人”。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯\n保险规划师:您好，您终于接电话了，看到您在泰康的保单一直没理赔过，是我们的高优客户，我们限时给您的保单升级成医保外的补充医疗，以后医社保报销完，剩余的部分，我们额外再给您报一次，我这边就耽误您几分钟时间给您做个讲解好吧？\n用户:好\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到泰康的官方短信，好效保开头的，您看下收到了吧？\n用户:收到了\n保险规划师:然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着泰康600万医疗保障看到了吧？\n用户:嗯\n保险规划师:然后您核对下，信息是本人吧？\n用户:是\n保险规划师:这次机会确实非常难得，是咱们专门针对医社保做的补充，大小疾病大小意外都能保，进口药自费药都能报销，没有药品限制，合同约定花多少报多少，最高能报销600万，这样讲还好理解吧？\n用户:好的\n保险规划师:这个不是所有人都能参与，对职业和健康有一定要求，请问您是做什么职业的?\n用户:问这个干什么\n保险规划师:因为对职业有要求，怕影响您之后理赔，所以需要问您是做什么工作的？？？\n用户:那不用了\n```\n\n最后一轮对话中的用户情境是："}], "chosen": {"from": "gpt", "value": "无"}, "rejected": {"from": "gpt", "value": "职业:无业"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据对话记录收集用户情境。对话记录是通过电话向用户销售保险的对话记录，用户情境包括用户的工作、婚姻状态、财务状况、家庭成员情况以及已有保险情况。\n\n注意：\n1、对话记录是保险规划师通过电话跟用户沟通的对话记录，对话记录的内容是通过语音识别出来的，可能存在错别字、谐音字。\n2、只需收集用户最后一轮对话里包含的情境，并且只输出最后一轮对话里包含的情境。\n3、输出格式为“职业:具体职业”。如果收集到多个用户情境(例如职业和婚姻)则用\\n分割，例如“职业:具体职业\\n婚姻:具体婚姻状态”。其中用户情境包括:职业、婚姻、孩子数量、孩子阶段。无需输出未提及的用户情境。没有任何用户情境则输出“无”。\n4、如果用户说了多次同一个用户情境，需要总结成一个结果。前后不一致时，以后面为准。\n5、用户的职业除了常见职业外，还包括“家庭主妇”、“无业”、“自由职业”、“退休或老年人”。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯\n保险规划师:您好，您终于接电话了，看到您在泰康的保单一直没理赔过，是我们的高优客户，我们限时给您的保单升级成医保外的补充医疗，以后医社保报销完，剩余的部分，我们额外再给您报一次，我这边就耽误您几分钟时间给您做个讲解好吧？\n用户:好\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到泰康的官方短信，好效保开头的，您看下收到了吧？\n用户:收到了\n保险规划师:然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着泰康600万医疗保障看到了吧？\n用户:嗯\n保险规划师:然后您核对下，信息是本人吧？\n用户:是\n保险规划师:这次机会确实非常难得，是咱们专门针对医社保做的补充，大小疾病大小意外都能保，进口药自费药都能报销，没有药品限制，合同约定花多少报多少，最高能报销600万，这样讲还好理解吧？\n用户:好的\n保险规划师:这个不是所有人都能参与，对职业和健康有一定要求，请问您是做什么职业的?\n用户:问这个干什么\n保险规划师:因为对职业有要求，怕影响您之后理赔，所以需要问您是做什么工作的？？？\n用户:那不用了\n保险规划师:您先别着急拒绝我嘛，今天给您带到的服务特别实用，小到生活当中有个肠炎胃炎，头疼脑热，大到癌症肿瘤，都给您保齐保全了，以后您就不用担心医疗费的问题了，不过不是所有职业都能报销，所以需要问下您的职业是啥？？？\n用户:我是做生意的\n```\n\n最后一轮对话中的用户情境是："}], "chosen": {"from": "gpt", "value": "职业:做生意"}, "rejected": {"from": "gpt", "value": "无"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据对话记录收集用户情境。对话记录是通过电话向用户销售保险的对话记录，用户情境包括用户的工作、婚姻状态、财务状况、家庭成员情况以及已有保险情况。\n\n注意：\n1、对话记录是保险规划师通过电话跟用户沟通的对话记录，对话记录的内容是通过语音识别出来的，可能存在错别字、谐音字。\n2、只需收集用户最后一轮对话里包含的情境，并且只输出最后一轮对话里包含的情境。\n3、输出格式为“职业:具体职业”。如果收集到多个用户情境(例如职业和婚姻)则用\\n分割，例如“职业:具体职业\\n婚姻:具体婚姻状态”。其中用户情境包括:职业、婚姻、孩子数量、孩子阶段。无需输出未提及的用户情境。没有任何用户情境则输出“无”。\n4、如果用户说了多次同一个用户情境，需要总结成一个结果。前后不一致时，以后面为准。\n5、用户的职业除了常见职业外，还包括“家庭主妇”、“无业”、“自由职业”、“退休或老年人”。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯\n保险规划师:您好，您终于接电话了，看到您在泰康的保单一直没理赔过，是我们的高优客户，我们限时给您的保单升级成医保外的补充医疗，以后医社保报销完，剩余的部分，我们额外再给您报一次，我这边就耽误您几分钟时间给您做个讲解好吧？\n用户:好\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到泰康的官方短信，好效保开头的，您看下收到了吧？\n用户:收到了\n保险规划师:然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着泰康600万医疗保障看到了吧？\n用户:嗯\n保险规划师:然后您核对下，信息是本人吧？\n用户:是\n保险规划师:这次机会确实非常难得，是咱们专门针对医社保做的补充，大小疾病大小意外都能保，进口药自费药都能报销，没有药品限制，合同约定花多少报多少，最高能报销600万，这样讲还好理解吧？\n用户:好的\n保险规划师:这个不是所有人都能参与，对职业和健康有一定要求，请问您是做什么职业的?\n用户:问这个干什么\n保险规划师:因为对职业有要求，怕影响您之后理赔，所以需要问您是做什么工作的？？？\n用户:那不用了\n保险规划师:您先别着急拒绝我嘛，今天给您带到的服务特别实用，小到生活当中有个肠炎胃炎，头疼脑热，大到癌症肿瘤，都给您保齐保全了，以后您就不用担心医疗费的问题了，不过不是所有职业都能报销，所以需要问下您是做什么职业的？\n用户:送外卖的\n```\n\n最后一轮对话中的用户情境是："}], "chosen": {"from": "gpt", "value": "职业:外卖员"}, "rejected": {"from": "gpt", "value": "无"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据对话记录收集用户情境。对话记录是通过电话向用户销售保险的对话记录，用户情境包括用户的工作、婚姻状态、财务状况、家庭成员情况以及已有保险情况。\n\n注意：\n1、对话记录是保险规划师通过电话跟用户沟通的对话记录，对话记录的内容是通过语音识别出来的，可能存在错别字、谐音字。\n2、只需收集用户最后一轮对话里包含的情境，并且只输出最后一轮对话里包含的情境。\n3、输出格式为“职业:具体职业”。如果收集到多个用户情境(例如职业和婚姻)则用\\n分割，例如“职业:具体职业\\n婚姻:具体婚姻状态”。其中用户情境包括:职业、婚姻、孩子数量、孩子阶段。无需输出未提及的用户情境。没有任何用户情境则输出“无”。\n4、如果用户说了多次同一个用户情境，需要总结成一个结果。前后不一致时，以后面为准。\n5、用户的职业除了常见职业外，还包括“家庭主妇”、“无业”、“自由职业”、“退休或老年人”。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯\n保险规划师:您好，您终于接电话了，看到您在泰康的保单一直没理赔过，是我们的高优客户，我们限时给您的保单升级成医保外的补充医疗，以后医社保报销完，剩余的部分，我们额外再给您报一次，我这边就耽误您几分钟时间给您做个讲解好吧？\n用户:好\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到泰康的官方短信，好效保开头的，您看下收到了吧？\n用户:收到了\n保险规划师:然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着泰康600万医疗保障看到了吧？\n用户:嗯\n保险规划师:然后您核对下，信息是本人吧？\n用户:是\n保险规划师:这次机会确实非常难得，是咱们专门针对医社保做的补充，大小疾病大小意外都能保，进口药自费药都能报销，没有药品限制，合同约定花多少报多少，最高能报销600万，这样讲还好理解吧？\n用户:好的\n保险规划师:这个不是所有人都能参与，对职业和健康有一定要求，请问您是做什么职业的?\n用户:问这个干什么\n保险规划师:因为对职业有要求，怕影响您之后理赔，所以需要问您是做什么工作的？？？\n用户:那不用了\n保险规划师:您先别着急拒绝我嘛，今天给您带到的服务特别实用，小到生活当中有个肠炎胃炎，头疼脑热，大到癌症肿瘤，都给您保齐保全了，以后您就不用担心医疗费的问题了，不过不是所有职业都能报销，所以需要问下您是做什么职业的？\n用户:包工程的\n```\n\n最后一轮对话中的用户情境是："}], "chosen": {"from": "gpt", "value": "职业:包工头"}, "rejected": {"from": "gpt", "value": "包工头"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据对话记录收集用户情境。对话记录是通过电话向用户销售保险的对话记录，用户情境包括用户的工作、婚姻状态、财务状况、家庭成员情况以及已有保险情况。\n\n注意：\n1、对话记录是保险规划师通过电话跟用户沟通的对话记录，对话记录的内容是通过语音识别出来的，可能存在错别字、谐音字。\n2、只需收集用户最后一轮对话里包含的情境，并且只输出最后一轮对话里包含的情境。\n3、输出格式为“职业:具体职业”。如果收集到多个用户情境(例如职业和婚姻)则用\\n分割，例如“职业:具体职业\\n婚姻:具体婚姻状态”。其中用户情境包括:职业、婚姻、孩子数量、孩子阶段。无需输出未提及的用户情境。没有任何用户情境则输出“无”。\n4、如果用户说了多次同一个用户情境，需要总结成一个结果。前后不一致时，以后面为准。\n5、用户的职业除了常见职业外，还包括“家庭主妇”、“无业”、“自由职业”、“退休或老年人”。\n\n对话记录：\n```\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯\n保险规划师:您好，您终于接电话了，看到您在泰康的保单一直没理赔过，是我们的高优客户，我们限时给您的保单升级成医保外的补充医疗，以后医社保报销完，剩余的部分，我们额外再给您报一次，我这边就耽误您几分钟时间给您做个讲解好吧？\n用户:好\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到泰康的官方短信，好效保开头的，您看下收到了吧？\n用户:收到了\n保险规划师:然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着泰康600万医疗保障看到了吧？\n用户:嗯\n保险规划师:然后您核对下，信息是本人吧？\n用户:是\n保险规划师:这次机会确实非常难得，是咱们专门针对医社保做的补充，大小疾病大小意外都能保，进口药自费药都能报销，没有药品限制，合同约定花多少报多少，最高能报销600万，这样讲还好理解吧？\n用户:好的\n保险规划师:这个不是所有人都能参与，对职业和健康有一定要求，请问您是做什么职业的?\n用户:问这个干什么\n保险规划师:因为对职业有要求，怕影响您之后理赔，所以需要问您是做什么工作的？？？\n用户:那不用了\n保险规划师:您先别着急拒绝我嘛，今天给您带到的服务特别实用，小到生活当中有个肠炎胃炎，头疼脑热，大到癌症肿瘤，都给您保齐保全了，以后您就不用担心医疗费的问题了，不过不是所有职业都能报销，所以需要问下您是做什么职业的？\n用户:在家带小孩\n```\n\n最后一轮对话中的用户情境是："}], "chosen": {"from": "gpt", "value": "职业:家庭主妇\n婚姻:已婚\n孩子数量:至少1个\n孩子阶段:婴幼儿"}, "rejected": {"from": "gpt", "value": "家庭主妇"}}, {"conversations": [{"from": "human", "value": "你是一名专业的保险规划师，你的任务是根据对话记录收集用户情境。对话记录是通过电话向用户销售保险的对话记录，用户情境包括用户的工作、婚姻状态、财务状况、家庭成员情况以及已有保险情况。\n\n注意：\n1、对话记录是保险规划师通过电话跟用户沟通的对话记录，对话记录的内容是通过语音识别出来的，可能存在错别字、谐音字。\n2、只需收集用户最后一轮对话里包含的情境，并且只输出最后一轮对话里包含的情境。\n3、输出格式为“职业:具体职业”。如果收集到多个用户情境(例如职业和婚姻)则用\\n分割，例如“职业:具体职业\\n婚姻:具体婚姻状态”。其中用户情境包括:职业、婚姻、孩子数量、孩子阶段。无需输出未提及的用户情境。没有任何用户情境则输出“无”。\n4、如果用户说了多次同一个用户情境，需要总结成一个结果。前后不一致时，以后面为准。\n5、用户的职业除了常见职业外，还包括“家庭主妇”、“无业”、“自由职业”、“退休或老年人”。\n\n对话记录：\n\"\"\"\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:啊，对\n保险规划师:您好，不好意思跟您说晚了，来电是我们整理老客户保单的时候，发现您在泰康有个补充医保的报销权益已经生效了，但是保单还没收到，这样日后就没办法理赔了，今天就是带您去看下保单，以后医保不报的进口药自费药就都能给您报了，100%报销，占用您2分钟宝贵时间我给您讲解下好吧？\n用户:我先问我先问你一下，你这个是什么保\n保险规划师:就是给您升级成医保外的补充医疗，这边带您去看下保单确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:这你先等\n用户:你先等你先等一下等。\n保险规划师:嗯，不着急，您看下，好效保开头的短信收到了吗？？？\n用户:你等一下，我问你，这这个是保多长时间的。\n保险规划师:这个就是交多久保多久，最高能保到99周岁您稍等下，可能有些延迟，您不要挂电话，打开免提，应该给您发过去了，好效保开头的，看到了吧？\n用户:不是我说你这一次这，你说这\n用户:你说这哎呀，你可真是\n用户:(用户没说话)\n保险规划师:这是一个全险，大小疾病大小意外都能保，进口药自费药都能赔，我带您去看下保单，您看下好效保开头的短信收到了吗？\n用户:收到了\n保险规划师:那您点开短信上的蓝色英文字母链接，点开就能进入泰康官网页面，600万医疗保障看到了吧？？？\n用户:我这个\n保险规划师:然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着泰康600万医疗保障看到了吧？\n用户:嗯，看到了。\n保险规划师:然后您核对下，信息是本人吧？\n用户:是本人。\n保险规划师:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，进口药自费药也能报，基础版每个月都是1.9元，没有额外费用，您往下滑，看下左下角费用是1.9元吧？\n用户:嗯，是一块九。\n保险规划师:虽然说费用比较低，但是呢，不是所有职业都能参加，对咱职业有要求，也是想问一下,您是做什么职业的呀？\n用户:没工作\n\"\"\"\n\n最后一轮对话中的用户情境是："}], "chosen": {"from": "gpt", "value": "职业:无业"}, "rejected": {"from": "gpt", "value": "职业:无"}}]
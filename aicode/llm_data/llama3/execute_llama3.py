# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2024/7/25 18:43
import datetime

from llama3.download.badcase下载_llama3 import execute as execute_download
from llama3.csv_to_json.csv_validation import execute as execute_csv_validation
from llama3.csv_to_json.csv_json import to_json
from llama3.json_upload.gather_json import execute as execute_json_upload

update_date = ''
if not update_date:
    current_date = datetime.datetime.now()
    update_date = current_date.strftime("%Y/%-m/%-d")
    print(update_date)

execute_download()
execute_csv_validation('./data/download_csv', './data/trustable_csv/', update_date)
to_json('./data/trustable_csv', './data/trustable_json', update_date)
execute_json_upload(['./data/all_json', './data/trustable_json'], outputjson_path='./data/output')

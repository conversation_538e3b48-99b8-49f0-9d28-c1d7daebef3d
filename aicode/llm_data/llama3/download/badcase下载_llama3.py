# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2024/1/12 10:21

import os.path
import pickle
import time
from loguru import logger
from selenium import webdriver
from selenium.webdriver import ActionChains
from selenium.webdriver.common.by import By
import os
import shutil
from llama3.download.login import execute as execute_login

current_path = os.path.abspath(__file__)
current_dir = os.path.dirname(current_path)
father_dir = os.path.dirname(current_dir)
parent_dir = os.path.dirname(current_dir)
grand_parent_dir = os.path.dirname(parent_dir)
db_file = grand_parent_dir + '/temp/cookies.pickle'


def delete_files_in_folder(folder_path):
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        try:
            if os.path.isfile(file_path) or os.path.islink(file_path):
                os.unlink(file_path)
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)
        except Exception as e:
            print('Failed to delete %s. Reason: %s' % (file_path, e))
    logger.info('历史文件已清空')


def init_driver():
    chrome_options = webdriver.ChromeOptions()
    # chrome_options.add_argument('--headless')
    prefs = {"download.default_directory": f"{father_dir}/data/download_csv/"}  # 指定下载路径
    chrome_options.add_experimental_option("prefs", prefs)
    driver = webdriver.Chrome(options=chrome_options)
    return driver


def save_cookies(driver_):
    cookies_ = driver_.get_cookies()
    with open(db_file, 'wb') as f:
        pickle.dump(cookies_, f)


def login(driver_):
    if os.path.exists(db_file):
        driver_.get('https://doc.weixin.qq.com/home/<USER>')
        with open(db_file, 'rb') as f:
            cookies = pickle.load(f)
            for cookie in cookies:
                driver_.add_cookie(cookie)
            driver_.refresh()
    else:
        driver_.get('https://doc.weixin.qq.com/scenario/login.html')
        for i in range(60):
            print(i)
            time.sleep(1)
            if driver_.current_url == 'https://doc.weixin.qq.com/home/<USER>':
                break
        save_cookies(driver_)


def download(driver_, url):
    logger.info("1")
    driver_.get('https://doc.weixin.qq.com/sheet/e3_AG8AagYdAHw1ej0s70KTtijWvGMKG?scode=AH0AJQdoAAwGUXSGLJAG8AagYdAHw&tab=' + url)
    logger.info("2")
    time.sleep(30)
    logger.info("3")
    element = driver_.find_element(By.ID, 'headerbar-filemenu')
    element.click()
    time.sleep(3)
    logger.info("4")
    element = driver_.find_element(By.XPATH, "//*[text()='导出']")
    hover = ActionChains(driver_).move_to_element(element)
    hover.perform()
    hover = ActionChains(driver_).move_to_element(element)
    hover.perform()
    time.sleep(3)
    logger.info("5")
    element = driver_.find_element(By.CLASS_NAME, "mainmenu-item-export-csv")
    print(element.text)
    element.click()
    # element = driver_.find_element(By.XPATH, "//*[text()='本地CSV文件（.csv，当前工作表）']")
    # print(element.text)
    # element.click()

    time.sleep(5)
    logger.info("6")


def execute():
    # execute_login()
    delete_files_in_folder(f"{father_dir}/data/download_csv/")
    logger.info('开始下载')
    driver = init_driver()
    login(driver)
    tabs = [
        ('BB08J2', '王斌-llama3'),
        ('6qtmw0', '帅超-llama3'),
        ('ivj9iu', '士祥-llama3'),
        ('9nd5l9', '傅强-llama3'),
        ('ups0p6', '亚玲-llama3'),
    ]
    for tab in tabs:
        logger.info('下载{}的badcase-开始'.format(tab[1]))
        download(driver, tab[0])
        logger.info('下载{}的badcase-已下载完成'.format(tab[1]))
    logger.info('下载结束')
    save_cookies(driver)
    time.sleep(1)
    driver.quit()
    logger.info('关闭浏览器')


if __name__ == '__main__':
    print(current_dir)
    execute()

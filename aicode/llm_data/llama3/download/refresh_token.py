# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2024/1/17 16:53
import os
import pickle
import time
from selenium import webdriver

current_path = os.path.abspath(__file__)
current_dir = os.path.dirname(current_path)
parent_dir = os.path.dirname(current_dir)
grand_parent_dir = os.path.dirname(parent_dir)
db_file = grand_parent_dir + '/temp/cookies.pickle'

def init_driver():
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument('--headless')
    prefs = {"download.default_directory": "/Users/<USER>/PycharmProjects/ujd_ai/llm_训练数据处理/训练数据生成/csv/badcase_下载/"}  # 指定下载路径
    chrome_options.add_experimental_option("prefs", prefs)
    driver_ = webdriver.Chrome(options=chrome_options)
    return driver_


def save_cookies(driver_):
    cookies_ = driver_.get_cookies()
    with open(db_file, 'wb') as f:
        print(cookies_)
        pickle.dump(cookies_, f)


def login(driver_):
    driver_.get('https://doc.weixin.qq.com/')

    # 每30分钟执行一次循环
    while True:
        try:
            if os.path.exists(db_file):
                with open(db_file, 'rb') as f:
                    cookies = pickle.load(f)
                    for cookie in cookies:
                        driver_.add_cookie(cookie)
                    time.sleep(5)
                    driver_.refresh()
                    time.sleep(5)
                    save_cookies(driver_)
            else:
                driver_.get('https://doc.weixin.qq.com/scenario/login.html')
                for i in range(60):
                    time.sleep(1)
                    if driver_.current_url == 'https://doc.weixin.qq.com/home/<USER>':
                        break
                save_cookies(driver_)
            time.sleep(1800)
        except Exception as e:
            print(e)
    driver.quit()


if __name__ == '__main__':
    driver = init_driver()
    login(driver)

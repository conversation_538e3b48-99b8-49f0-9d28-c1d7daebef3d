# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2024/1/12 14:23
import json
import os
import re

import pandas as pd
import requests

sops = set([])
questions = set([])

def send_msg(content):
    webhook_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2cf48df7-0ff8-47e3-9cac-74b4b21f6120'
    headers = {'Content-Type': 'application/json'}
    data = {
        "msgtype": "markdown",
        "markdown": {
            "content": content,
        }
    }
    dumps = json.dumps(data, ensure_ascii=False).replace('<br>', r'\n\n')
    response = requests.post(
        url=webhook_url,
        headers=headers,
        data=dumps.encode('utf-8')
    )

# 定义一个函数,递归获取一个目录下的所有csv文件
def get_json_files(directory):
    files = []
    for filename in os.listdir(directory):
        if filename.endswith('.json'):
            files.append(os.path.join(directory, filename))
        elif os.path.isdir(os.path.join(directory, filename)):
            json_files = get_json_files(os.path.join(directory, filename))
            files.extend(json_files)
    return files


def merge_json_files(directory):
    data = []
    data2 = []
    files = get_json_files(directory)
    for filename in files:
        with open(filename, 'r', encoding='utf8') as f:
            file_data = json.load(f)
            if isinstance(file_data, list):
                data.extend(file_data)
            else:
                print(f"Warning: data in {filename} is not a list and has been skipped.")
    for d in data:
        d['conversations'][0]['value'] = str(d['conversations'][0]['value']).strip()
        d['conversations'][1]['value'] = str(d['conversations'][1]['value']).strip()
        if '判断用户的问题，也就是疑问、不满和诉求是什么' in d['conversations'][0]['value']:
            d['conversations'][1]['value'] = str(d['conversations'][1]['value'])\
                .replace('再次表示', '')\
                .replace('多次表示', '')\
                .replace('再次', '')\
                .replace('多次', '')
            questions.add(d['conversations'][1]['value'])
        if '选择销售流程下一步的销售流程节点' in d['conversations'][0]['value']:
            sops.add(d['conversations'][1]['value'])


def check_rule(prompt, output):
    if '根据下面提示一步一步执行,不要无中生有。' in prompt:
        task = '话术选择'
        match = re.match(r'^[a-zA-Z0-9|\-]+$', output)
        if match:
            return True, ''
        else:
            return False, f'[{task}]的prompt输出为[{output}],输出内容不符合规则!'
    elif '疑问、不满和诉求是什么。' in prompt:
        task = '用户疑义'
        if output in questions:
            return True, ''
        else:
            return False, f'[{task}]的prompt输出为[{output}],第一次出现这个用户疑义!'
    elif '下一步的销售流程节点' in prompt:
        task = '流程节点'
        if output in sops:
            return True, ''
        else:
            return False, f'[{task}]的prompt输出为[{output}],第一次出现这个流程节点!'
    elif '明确同意、倾向同意、疑似同意、中立、倾向拒绝、明确拒绝' in prompt:
        task = '赠险意向'
        if output in ['明确同意', '倾向同意', '疑似同意', '中立', '倾向拒绝', '明确拒绝']:
            return True, ''
        else:
            return False, f'[{task}]的prompt输出为[{output}],输出内容不符合规则!'
    else:
        return False, f''


def execute(directory, output_dir, update_date):
    merge_json_files('./json')
    error_info = ''
    warning_info = ''
    date = update_date.split('/')
    update_date2 = date[1] + '.' + date[2]
    update_date3 = date[1] + ' 月 ' + date[2] + '日'
    update_date_filename = date[0] + '-' + date[1] + '-' + date[2]
    files = [file for file in os.listdir(directory) if file.endswith('.csv')]
    info = {}
    for file in files:
        df = pd.read_csv(directory + '/' + file, encoding='utf-8')
        datas = df.to_dict('records')
        info[file] = {'日期': update_date, '数据量:': 0, '异常数据': 0}
        date_list = []
        search = re.search('通用大模型-badcase-(.*?).csv', file)
        user = ''
        if search:
            user = search.group(1)
        for data in datas:
            if data['修改日期'] in (update_date, update_date2, update_date3):
                prompt = data['prompt']
                badcase_output = data['原结果']
                output = data['修改后结果']
                if not prompt or not isinstance(prompt, str) or not output or not isinstance(output, str) or output == '' or '?' in output or '？' in output:
                    if not isinstance(prompt, float):
                        info[file]['异常数据'] += 1
                    continue
                result, err = check_rule(prompt, output)
                if not result:
                    print(err)
                    error_info_temp = f'<font color="warning">{user}:{err}</font>\n'
                    if error_info_temp not in error_info:
                        error_info += error_info_temp
                info[file]['数据量:'] += 1
                date_list.append({
                    'prompt': prompt,
                    '原结果': badcase_output,
                    'output': output
                })
        if len(date_list):
            pd.DataFrame(date_list).to_csv(output_dir + update_date_filename + file.replace('case.csv', '.csv'), encoding='utf-8', index=False)

        log = '日期:{},数据量:{},异常量:{}'.format(update_date, info[file]['数据量:'], info[file]['异常数据'])
        print(log)
        if info[file]['数据量:'] > 0 and info[file]['异常数据'] == 0:
            warning_info += f'<font color="info">{user}-{log}</font>\n'
        elif info[file]['数据量:'] > 0 and info[file]['异常数据'] > 0:
            warning_info += f'<font color="error">{user}-{log}</font>\n'
    send_msg(error_info)
    send_msg(warning_info)


if __name__ == '__main__':
    execute('csv/badcase_下载', 'csv/通用模型badcase/', '2024/3/4')


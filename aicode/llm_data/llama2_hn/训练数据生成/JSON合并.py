# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2023/11/23 22:04
import datetime
import json
import os
import uuid
from random import shuffle
from SSH import upload_json, sed_replace_date


# 定义一个函数,递归获取一个目录下的所有csv文件
def get_json_files(directory):
    files = []
    for filename in os.listdir(directory):
        if filename.endswith('.json'):
            files.append(os.path.join(directory, filename))
        elif os.path.isdir(os.path.join(directory, filename)):
            json_files = get_json_files(os.path.join(directory, filename))
            files.extend(json_files)
    return files


def get_json_files_not_zzd(directory):
    files = []
    for filename in os.listdir(directory):
        if filename.endswith('.json') and not filename.endswith('-zzd.json'):
            files.append(os.path.join(directory, filename))
        elif os.path.isdir(os.path.join(directory, filename)):
            json_files = get_json_files_not_zzd(os.path.join(directory, filename))
            files.extend(json_files)
    return files


def merge_json_files(directory, output_filename):
    data = []
    data2 = []
    files = get_json_files_not_zzd(directory)
    for filename in files:
        with open(filename, 'r', encoding='utf8') as f:
            file_data = json.load(f)
            if isinstance(file_data, list):
                data.extend(file_data)
            else:
                print(f"Warning: data in {filename} is not a list and has been skipped.")
    for d in data:
        d['conversations'][0]['value'] = str(d['conversations'][0]['value']).strip()
        d['conversations'][1]['value'] = str(d['conversations'][1]['value']).strip()
        if '判断用户的问题，也就是疑问、不满和诉求是什么' in d['conversations'][0]['value']:
            d['conversations'][1]['value'] = str(d['conversations'][1]['value'])\
                .replace('再次表示', '')\
                .replace('多次表示', '')\
                .replace('再次', '')\
                .replace('多次', '')
        d['id'] = str(uuid.uuid1())
        data2.append(d)
    shuffle(data2)
    with open(output_filename, 'w', encoding='utf8') as f:
        json.dump(data2, f, ensure_ascii=False, indent=4)


def execute():
    current_date = datetime.datetime.now()
    date_string = current_date.strftime("%Y-%m-%d")
    filename = f'{date_string}.json'
    merge_json_files('./json', f'./output/{filename}')

    upload_json('*************', filename)
    sed_replace_date('*************', filename, script_name='llm_train.sh')
    sed_replace_date('*************', filename, script_name='llm_traina.sh')
    sed_replace_date('*************', filename, script_name='llm_trainb.sh')
    sed_replace_date('*************', filename, script_name='llm_trainc.sh')
    sed_replace_date('*************', filename, script_name='llm_traind.sh')


if __name__ == '__main__':
    execute()

# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2024/1/12 14:23
import datetime

from llama2_hn.badcase下载 import badcase下载
from llama2_hn.训练数据生成 import badcase清洗过滤
from llama2_hn.训练数据生成 import badcase转换格式
from llama2_hn.训练数据生成 import JSON合并
update_date = ''
if not update_date:
    current_date = datetime.datetime.now()
    update_date = current_date.strftime("%Y/%-m/%-d")
    print(update_date)
badcase下载.execute()
badcase清洗过滤.execute('csv/badcase_下载', 'csv/通用模型badcase/', update_date)
badcase转换格式.to_json('csv/通用模型badcase', 'json/badcase', update_date)

JSON合并.execute()

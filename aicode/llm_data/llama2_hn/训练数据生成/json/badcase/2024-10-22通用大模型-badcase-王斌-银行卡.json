[{"id": "44aa6a62-905e-11ef-bf37-aa940f1afa1a", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:是的。\n保险规划师:您好，不好意思跟您说晚了，来电是我们整理老客户保单的时候，发现您在泰康有个补充医保的报销权益已经生效了，但是保单还没收到，这样日后就没办法理赔了，今天就是带您去看下保单，以后医保不报的进口药自费药就都能给您报了，100%报销，占用您2分钟宝贵时间我给您讲解下好吧？\n用户:呃，我\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:我老老\n用户:有些显是我老人，这个手机他已经去世了，手机是我在用，他把密码改了，我不知道\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "家人接听电话-人已去世"}]}, {"id": "44aa7502-905e-11ef-bf37-aa940f1afa1a", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:是再说嘛。\n保险规划师:您好，不好意思跟您说晚了，来电是我们整理老客户保单的时候，发现您在泰康有个补充医保的报销权益已经生效了，但是保单还没收到，这样日后就没办法理赔了，今天就是带您去看下保单，以后医保不报的进口药自费药就都能给您报了，100%报销，占用您2分钟宝贵时间我给您讲解下好吧？\n用户:好的你讲啊。\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:那我退一下。\n保险规划师:您稍等下，可能有些延迟，您不要挂电话，打开免提，应该给您发过去了，好效保开头的，看到了吧？\n用户:哦，看到了。\n保险规划师:那您点开短信上的蓝色英文字母链接，点开就能进入泰康官网页面，600万医疗保障看到了吧？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？\n用户:听得到\n保险规划师:然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着泰康600万医疗保障看到了吧？\n用户:哎，别填完了，不需要了。\n保险规划师:这个您别着急拒绝我嘛，这个就是给您的保单做个升级，保障更加全面了，大小疾病大小意外全部保障到了，进口药自费药都能报销，您先点开链接，带您看下保障内容，（停顿下）打开了吧？？？\n用户:哎，那你钱别的。\n用户:借借了二十几万，现在也没大用。\n保险规划师:抱歉，刚刚没听清，麻烦您再说下？？？\n用户:我说别骗完了也不用了。\n保险规划师:泰康保险是世界五百强企业，保险都是在国家监管之下的，这个您可以放心这个是报销医疗费用的，以后不管是意外还是生病只有住院治疗都能报销，医社保报销完剩余的部分除去免赔是100%报销的，不需要自己花钱了，您把链接打开我带您看下保单，（停顿下）链接点开了嘛？有没有看到蓝色背景的600万医疗保障？\n用户:哎，现在还入这个机会保了呢。\n保险规划师:嗯，是的，您就点下我已阅读并同意投保，然后健康没问题，您点右下角的符合，然后打开支付宝，（停顿下）打开了嘛？？？\n用户:这保险了。\n用户:(用户没说话)\n保险规划师:不过不是所有的客户都能参加，对健康有个审核，您点下我已阅读并同意投保，然后健康没问题，您点右下角的符合，然后打开支付宝，（停顿下）打开了嘛？\n用户:呃，支付宝了。\n保险规划师:打开支付宝，您确认下这个费用是不是1.9元。是的话您支付下，保障从今晚12点开始生效，以后医疗费用咱们就不用担心了，您支付下，支付好了告诉我，我给您登记下（停顿下）支付好了吧？\n用户:哎，不需要了，你好几个保险了，入的\n保险规划师:那您保险意识挺不错的，这个是跟您的医保商保做补充的，把医保不报的进口药自费药给您报了，进口药自费药也能报，费用也很轻松，基础版一个月就一两块钱，您放心参加就行好吧？？？\n用户:哎呀，我这个机会保金，惠保孩子的医保，你那个冀惠保这个区，那个冀惠保缺个报销了。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "百万医疗与惠民保什么关系"}]}]
[{"id": "92b833ec-d401-11ef-84ec-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:哎，\n保险规划师:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你说你是干什么的？\n保险规划师:就是带您去领取下您的保单，教您怎么去理赔，我带您去微信公众号上看下，您电话不要挂，打开免提，然后把微信打开，（停顿下）微信打开了吧？？？\n用户:我都我都没有，我都我都\n用户:我从我都不用买医保啊。\n用户:我没有买医保啊。\n保险规划师:能理解您，不过人吃五谷杂粮，难免不生病，就是多个保障以防万一，以后医疗费用咱们就不用担心了，您先了解下好吧？？？\n用户:能理解您\n用户:医疗费\n用户:你说怎么弄？\n保险规划师:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n用户:嗯，\n用户:嗯，\n用户:公众号号码多少？\n保险规划师:咱们专属公众号是“真情宝”，真是真假的真，情是情感的情，宝是宝贝的宝，宝物的宝，真情宝，您输入下，输好之后点下搜索，然后下方会出现真情宝的公众号，看到了吧？？？\n用户:嗯，\n保险规划师:您关注下“真情宝”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，\n用户:看到了\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:输入了\n用户:嗯，\n保险规划师:您好，您是不是没输好啊，我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:输入了七七九零吧。\n保险规划师:这边看到您还没输好啊，我的工号是xxxxxx（播报4位数激活码），您操作到哪个页面了啊？\n用户:对。\n用户:输入了九九七零了。\n保险规划师:升级之后是一个全险，以后咱们大小疾病大小意外都能保，进口药自费药都能报销，没有药品限制，合同约定花多少报多少，最高能报销300万，还可以限时领取一个免费的全身体检卡，您输入下我的工号******（播报4位数激活码），然后能看到，人保无忧医疗险赠全年体检权益。有看到这个页面吗？\n用户:看到了\n保险规划师:那之所以给您升级这个保障，是考虑到人吃五谷生百病，加上现在的生活环境，吃的用的都不太卫生，难免要跟医院打交道，万一生个大病需要个几万几十万，不仅自己压力很大，同时也会影响家人的正常生活，保障完善之后以后医疗费用咱们就不用担心了，都给您保障到了，您输入下我的工号xxxx（播报4位数激活码），我先带您去看下好吧？\n用户:你才万一生个大病呢？\n用户:你万一生个大病。\n用户:我倒。\n用户:你万一生个大病。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "觉得不吉利"}]}, {"id": "92b84576-d401-11ef-84ec-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:哎，\n保险规划师:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你说你是干什么的？\n保险规划师:就是带您去领取下您的保单，教您怎么去理赔，我带您去微信公众号上看下，您电话不要挂，打开免提，然后把微信打开，（停顿下）微信打开了吧？？？\n用户:我都我都没有，我都我都\n用户:我从我都不用买医保啊。\n用户:我没有买医保啊。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "不需要百万医疗-没社保"}]}, {"id": "92b84e7c-d401-11ef-84ec-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯，是的。\n保险规划师:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，好的。\n保险规划师:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:你是哪单位？\n保险规划师:是中国人保给您承保的，人保是国企，中国前四大保险公司，今天就是带您去看下补充医保的电子保单我带您到我们微信公众号上看下，您打开免提，回到手机主屏幕，然后打开微信，您打开微信和我说一下，我带您操作看一下 （停顿下）您微信打开了吧？\n用户:你是哪个单位的？\n保险规划师:这个保障是由中国人保给您提供的，补充医社保报销的您好，您微信打开了吗，我带您去我们官方公众号上看下保单（停顿下）微信打开了吧？\n用户:我打开\n用户:你把你，你把你的手机号码，你把你手机号打电话过来，好不好。\n保险规划师:抱歉，保护您的隐私，我们不能私下加您微信，都是在线直接通知的您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行或者最后一行有公众号这三个字，请问您这边有看到吗？\n用户:你的，你的电话是多少？\n用户:你的电话是多少？\n用户:你是不是做小姐的？\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "调侃"}]}]
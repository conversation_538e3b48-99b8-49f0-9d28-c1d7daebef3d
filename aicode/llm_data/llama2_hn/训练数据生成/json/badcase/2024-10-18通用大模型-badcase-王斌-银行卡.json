[{"id": "3ba128cc-8d41-11ef-a574-aa940f1afa1a", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:他是谁？\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:号码。\n保险规划师:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:喂。\n用户:嗯，\n用户:打开了\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:看了。\n用户:嗯，\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:咋的了？\n保险规划师:就是带您去我们公众号上看下您之前的保单就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:啊嗯。\n用户:嗯，\n保险规划师:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:不会钱是吧。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "百万医疗是否收费"}]}, {"id": "3ba132ea-8d41-11ef-a574-aa940f1afa1a", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:(用户没说话)\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:喂。\n用户:你是哪哪个保险呀？\n保险规划师:是泰康保险给您承保的，泰康是世界五百强企业，中国前四大保险公司，今天就是带您去看下补充医保的电子保单我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:哦。\n用户:哦。\n用户:啊。\n用户:啊。\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:啥什么公众号？\n保险规划师:咱们专属公众号是“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，您输入下，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:哦。\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，然后呢。\n用户:行了。\n保险规划师:然后您点进去，关注下，关注好了之后，在右下角有个我的，您点下，然后第一行有个贵宾通道，看到了吧？\n用户:嗯，嗯嗯。\n用户:嗯，\n用户:啊，你知道我那个\n用户:还什么贵宾通道？\n保险规划师:就在有康保公众号里边，选项我的第一行就可以找到了然后您点下贵宾通道，然后输入我的工号xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:嗯嗯。\n保险规划师:我的工号是xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:啊。\n用户:嗯，嗯嗯，然后呢。\n用户:然后呢。\n保险规划师:您输好之后会进入泰康官网页面，泰康惠民健康保，赠全年体检权益，有看到吧\n用户:这显示什么？一天多少多少钱啊。\n保险规划师:这个每个月都是0.6元，固定不变，您点下右下角的立即投保，带您去领取下保单和体检卡，（停顿下）立即投保点了吧？？？\n用户:啥呀？立即投保了，就\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "百万医疗是什么"}]}, {"id": "3ba13b46-8d41-11ef-a574-aa940f1afa1a", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:(用户没说话)\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:喂。\n用户:你是哪哪个保险呀？\n保险规划师:是泰康保险给您承保的，泰康是世界五百强企业，中国前四大保险公司，今天就是带您去看下补充医保的电子保单我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:哦。\n用户:哦。\n用户:啊。\n用户:啊。\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:啥什么公众号？\n保险规划师:咱们专属公众号是“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，您输入下，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:哦。\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，然后呢。\n用户:行了。\n保险规划师:然后您点进去，关注下，关注好了之后，在右下角有个我的，您点下，然后第一行有个贵宾通道，看到了吧？\n用户:嗯，嗯嗯。\n用户:嗯，\n用户:啊，你知道我那个\n用户:还什么贵宾通道？\n保险规划师:就在有康保公众号里边，选项我的第一行就可以找到了然后您点下贵宾通道，然后输入我的工号xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:嗯嗯。\n保险规划师:我的工号是xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:啊。\n用户:嗯，嗯嗯，然后呢。\n用户:然后呢。\n保险规划师:您输好之后会进入泰康官网页面，泰康惠民健康保，赠全年体检权益，有看到吧\n用户:这显示什么？一天多少多少钱啊。\n保险规划师:这个每个月都是0.6元，固定不变，您点下右下角的立即投保，带您去领取下保单和体检卡，（停顿下）立即投保点了吧？？？\n用户:啥呀？立即投保了，就\n保险规划师:这边看到您还没提交啊，您就把页面往下滑，然后点右下角的立即投保，点好了嘛？\n用户:这每个月都这么多钱吗？\n保险规划师:这个每个月就6毛钱，费用固定不变，不会上涨那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，每个月都是0.6元，固定不变，不会上涨，没有额外费用，你点下立即投保，点了吧\n用户:嗯，\n保险规划师:这个保险是生活当中必需品，人人都要有，用不着保的就是平安与健康，万一用着了，就是花个小钱保着大钱的，因为0.6元对我们生活没有任何影响 ，就是一瓶水的钱呢，您点下立即投保，点好了嘛？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:那。\n保险规划师:升级之后大小疾病大小意外都能保，进口药自费药也都能报销，每个月都是0.6元，固定不变不会上涨，您点下立即投保，我带您去完善下然后领下体检卡（停顿下）立即投保点了吗？\n用户:嗯嗯。\n用户:你。\n用户:哦。\n用户:嗯，\n用户:好几天\n用户:(用户没说话)\n保险规划师:点好之后您看下是不是0.6元，是的话您支付下这6毛钱，保障从今晚12点生效，每个月6毛，固定不变，以后大小疾病大小意外只要住院治疗都能保，支付好了告诉我，我给您登记下，（停顿）支付好了吧？\n用户:一点都\n用户:钱已经开通了。\n用户:呃，\n用户:嗯，\n用户:啊，弄好了。\n用户:我已经支付好了，嗯嗯，嗯\n保险规划师:支付好之后点完成，然后会看到恭喜您成功投保医疗保障，看到了吧？\n用户:啊，对\n保险规划师:然后下面有个贵宾专享，这个是给您的专属补充权益，刚刚那个是事后报销的，这个是先拿钱后看病的，就是只要在医院检查确诊了合同约定的疾病，直接给您最高30万，这个钱也不用再还回来，您自由支配，您点下下面的保费测算\n用户:对写完了\n用户:(用户没说话)\n保险规划师:就是以后出现身体不舒服，到医院去做检查，检查出来不管是轻症疾病还是重大疾病，只要是检查出来了，在还没有看病住院治疗之前，我们就提前一次性先赔付最高30万元的保险金打到您的帐户里，您就不用花您自己的钱去看病住院，好理解吧。\n用户:这什么保费测算又\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "先拿钱后看病什么意思"}]}, {"id": "3ba14398-8d41-11ef-a574-aa940f1afa1a", "conversations": [{"from": "human", "value": "作为一个专业的保险销售员，业务场景是通过电话销售百万医疗保险，你需要根据对话记录选择销售流程下一步的销售流程节点。\n\n销售流程如下表格：\n\n节点|节点目标\n---|---\n来电目的|告知用户之前送的意外险，防止用户不会理赔，带用户去查看保单。\n打开微信|确认用户是否打开微信\n搜索框|用户打开微信后,第二步,要引导用户找到微信中如何添加公众号,用户拒绝需要劝服\n公众号|用户找到如何添加公众号后,第三步,要引导用户在微信中搜索【如果有保】并看到【如果有保】公众号，用户说“好的”、“嗯”代表用户已经看到了公众号，用户拒绝需要劝服\n点击菜单|用户看到公众号后,第四步,指导用户看到【如果有保】公众号中的指定按键,用户拒绝需要劝服\n输入数字|用户看到指定按键后,第五步,告知用户数字,指导用户输入数字后会自动进入页面,用户拒绝需要劝服\n短险页面|询问用户是否看到惠民保字样的页面\n确认费用|与用户确认页面展示的个人身份信息内容及费用是否正确\n立即投保|让用户点击立即投保按钮\n引导支付|引导用户支付费用\n完成支付|确认用户是否支付完成是否看到投保成功页，如果用户已经表示完成投保则进行下一步\n加保|用户支付成功后，让用户加保重疾险，告知用户后则进行下一步\n介绍重疾险|向用户介绍重疾险，告知用户后则进行下一步\n挂机|完成销售流程\n\n\n对话记录如下：\n```\n保险销售员:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你干啥？\n保险销售员:就是带您去领取下您的保单，教您怎么去理赔，我带您去微信公众号上看下，您电话不要挂，打开免提，然后把微信打开，（停顿下）微信打开了吧？？？\n用户:(用户没说话)\n保险销售员:您好，能听到嘛？？？\n用户:嗯，应该还是啥。\n用户:啊。\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:嗯，\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:(用户没说话)\n保险销售员:您好，能听到嘛？？？\n用户:嗯，\n保险销售员:就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:嗯，\n用户:嗯，\n保险销售员:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:(用户没说话)\n保险销售员:您好，能听到嘛？？？\n用户:嗯，\n保险销售员:我的工号是xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:嗯，嗯嗯。\n用户:嗯，\n保险销售员:您输好之后会进入泰康官网页面，泰康惠民健康保，赠全年体检权益，有看到吧\n用户:嗯，\n保险销售员:然后您核对下，信息是本人吧？\n用户:嗯，\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，每个月都是0.6元，固定不变，不会上涨，没有额外费用，你点下立即投保，点了吧\n用户:(用户没说话)\n保险销售员:您好，能听到嘛？？？\n用户:嗯，\n用户:嗯，嗯嗯。\n保险销售员:这个是医保报销完的，剩余的合同约定的我们都给您报销，每个月都是0.6元，固定不变不会上涨，没有额外费用，您点下立即投保，点了吗？\n用户:嗯，\n保险销售员:点好之后您看下是不是0.6元，是的话您支付下这6毛钱，保障从今晚12点生效，每个月6毛，固定不变，以后大小疾病大小意外只要住院治疗都能保，支付好了告诉我，我给您登记下，（停顿）支付好了吧？\n用户:嗯，\n保险销售员:您支付好了嘛？支付完成之后，您看下有没有看到恭喜您成功投保医疗保障，看到了吧？\n用户:嗯，\n保险销售员:然后下面有个贵宾专享，这个是给您的专属补充权益，刚刚那个是事后报销的，这个是先拿钱后看病的，就是只要在医院检查确诊了合同约定的疾病，直接给您最高30万，这个钱也不用再还回来，您自由支配，您点下下面的保费测算\n用户:(用户没说话)\n保险销售员:您好，能听到嘛？？？\n用户:正常。\n用户:正常。\n保险销售员:就是以后出现身体不舒服，到医院去做检查，检查出来不管是轻症疾病还是重大疾病，只要是检查出来了，在还没有看病住院治疗之前，我们就提前一次性先赔付最高30万元的保险金打到您的帐户里，您就不用花您自己的钱去看病住院，好理解吧。\n用户:这还要交钱\n\n```\n请注意:\n1.销售流程节点按照表格从上到下执行,同时也要注意节点目标是否完成,在不引起用户反感的情况下,要节点目标达成后再进行下一个节点\n2.根据保险销售员其他的话术一步一步确认销售流程的节点,但是需要忽略掉用户提出的问题,同时也忽略掉保险销售员对用户问题的解答\n3.你的目的是完成销售,忽略用户的负向反馈,继续走销售流程\n保险销售员的下一句的销售流程节点(仅选择销售流程表格中的一个节点)是:"}, {"from": "gpt", "value": "介绍重疾险"}]}, {"id": "3ba14cf8-8d41-11ef-a574-aa940f1afa1a", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你干啥？\n保险规划师:就是带您去领取下您的保单，教您怎么去理赔，我带您去微信公众号上看下，您电话不要挂，打开免提，然后把微信打开，（停顿下）微信打开了吧？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:嗯，应该还是啥。\n用户:啊。\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:嗯，\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:嗯，\n保险规划师:就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:嗯，\n用户:嗯，\n保险规划师:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:嗯，\n保险规划师:我的工号是xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:嗯，嗯嗯。\n用户:嗯，\n保险规划师:您输好之后会进入泰康官网页面，泰康惠民健康保，赠全年体检权益，有看到吧\n用户:嗯，\n保险规划师:然后您核对下，信息是本人吧？\n用户:嗯，\n保险规划师:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，每个月都是0.6元，固定不变，不会上涨，没有额外费用，你点下立即投保，点了吧\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:嗯，\n用户:嗯，嗯嗯。\n保险规划师:这个是医保报销完的，剩余的合同约定的我们都给您报销，每个月都是0.6元，固定不变不会上涨，没有额外费用，您点下立即投保，点了吗？\n用户:嗯，\n保险规划师:点好之后您看下是不是0.6元，是的话您支付下这6毛钱，保障从今晚12点生效，每个月6毛，固定不变，以后大小疾病大小意外只要住院治疗都能保，支付好了告诉我，我给您登记下，（停顿）支付好了吧？\n用户:嗯，\n保险规划师:您支付好了嘛？支付完成之后，您看下有没有看到恭喜您成功投保医疗保障，看到了吧？\n用户:嗯，\n保险规划师:然后下面有个贵宾专享，这个是给您的专属补充权益，刚刚那个是事后报销的，这个是先拿钱后看病的，就是只要在医院检查确诊了合同约定的疾病，直接给您最高30万，这个钱也不用再还回来，您自由支配，您点下下面的保费测算\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:正常。\n用户:正常。\n保险规划师:就是以后出现身体不舒服，到医院去做检查，检查出来不管是轻症疾病还是重大疾病，只要是检查出来了，在还没有看病住院治疗之前，我们就提前一次性先赔付最高30万元的保险金打到您的帐户里，您就不用花您自己的钱去看病住院，好理解吧。\n用户:这还要交钱\n保险规划师:然后下面有个贵宾专享，这个是给您的专属补充权益，刚刚那个是事后报销的，这个是先拿钱后看病的，就是只要在医院检查确诊了合同约定的疾病，直接给您最高30万，这个钱也不用再还回来，您自由支配，您点下下面的保费测算\n用户:是什么？\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "先拿钱后看病什么意思"}]}, {"id": "3ba15856-8d41-11ef-a574-aa940f1afa1a", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:你是。\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你是哪个保险公司的？\n保险规划师:这个保障是由泰康保险给您提供的，补充医社保报销的我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:你昨天是\n保险规划师:那您打开下，我带您看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:你是哪个公司的？\n保险规划师:是泰康保险给您承保的，泰康是世界五百强企业，中国前四大保险公司，今天就是带您去看下补充医保的电子保单您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:我找你们的保险吗？\n用户:我买过你们的保险吗？\n保险规划师:您之前有泰康一个免费的意外险，现在给您升级成全险打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:不是我买过你们的保险吗？\n保险规划师:您之前有泰康一个免费的意外险，现在给您升级成全险您好有看到公众号三个字嘛？就是您点下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，看到了吧？\n用户:不是你再给我从公众号看公众号干什么。\n用户:不是看他干什么，看这三个字干什么。\n保险规划师:就是带您去我们公众号上看下您之前的保单接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n保险规划师:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:在线你们是泰康\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "无"}]}, {"id": "3ba16562-8d41-11ef-a574-aa940f1afa1a", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:你是。\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你是哪个保险公司的？\n保险规划师:这个保障是由泰康保险给您提供的，补充医社保报销的我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:你昨天是\n保险规划师:那您打开下，我带您看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:你是哪个公司的？\n保险规划师:是泰康保险给您承保的，泰康是世界五百强企业，中国前四大保险公司，今天就是带您去看下补充医保的电子保单您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:我找你们的保险吗？\n用户:我买过你们的保险吗？\n保险规划师:您之前有泰康一个免费的意外险，现在给您升级成全险打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:不是我买过你们的保险吗？\n保险规划师:您之前有泰康一个免费的意外险，现在给您升级成全险您好有看到公众号三个字嘛？就是您点下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，看到了吧？\n用户:不是你再给我从公众号看公众号干什么。\n用户:不是看他干什么，看这三个字干什么。\n保险规划师:就是带您去我们公众号上看下您之前的保单接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n保险规划师:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:在线你们是泰康\n保险规划师:这个公众号就是专门服务老用户的，您放心。我的工号是xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:你们这\n用户:九五八八。\n保险规划师:您有看到“请输入专属工号”下边有四个框框，然后您输入一下我的工号******（播报6位数激活码），输好了没？\n用户:然后有一个姓名健康保\n用户:体检卡乱七八糟，套餐\n保险规划师:您输好之后会进入泰康官网页面，泰康惠民健康保，赠全年体检权益，有看到吧\n用户:嗯，\n保险规划师:然后您核对下，信息是本人吧？\n用户:嗯，\n保险规划师:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，每个月都是0.6元，固定不变，不会上涨，没有额外费用，你点下立即投保，点了吧\n用户:你这个每个月给报多少\n保险规划师:这个责任范围内，住院产生的医疗费用，不管什么药品都能报，不超过600万就可以升级之后大小疾病大小意外都能保，进口药自费药也都能报销，每个月都是0.6元，固定不变不会上涨，您点下立即投保，我带您去完善下然后领下体检卡（停顿下）立即投保点了吗？\n用户:你是百分之百报销呀，还是多少报\n用户:我还是了解呢，投什么保\n用户:我都不了解，你叫我投保，你都不，你都不说这是干啥的，你都叫我投保。\n保险规划师:就是一个医疗保险，责任范围内，报销就医医疗费用，进口药自费药都能报这个是医保报销完的，剩余的合同约定的我们都给您报销，每个月都是0.6元，固定不变不会上涨，没有额外费用，您点下立即投保，点了吗？\n用户:我问你，你先别说话。\n保险规划师:诶，您请说（加 2 秒空录音）？？？\n用户:你先别说话，你在听我说吗？\n保险规划师:诶，您说？？？\n用户:你这个报销是报销多少？\n保险规划师:这个责任范围内，住院产生的医疗费用，不管什么药品都能报，不超过600万就可以那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，每个月都是0.6元，固定不变，不会上涨，没有额外费用，你点下立即投保，点了吧\n用户:我知道\n用户:我问你的是\n用户:我问你的是\n用户:我问的是报销多少，比例多少。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "百万医疗报销比例"}]}, {"id": "3ba1752a-8d41-11ef-a574-aa940f1afa1a", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:是。\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险规划师:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:嗯，\n用户:嗯，\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n保险规划师:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:嗯，\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:嗯，\n用户:不会是骗人的吧。\n保险规划师:泰康保险是世界五百强企业，保险都是在国家监管之下的，这个您可以放心我的工号是xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:嗯，\n保险规划师:您输好之后会进入泰康官网页面，泰康惠民健康保，赠全年体检权益，有看到吧\n用户:嗯，\n保险规划师:然后您核对下，信息是本人吧？\n用户:嗯，\n保险规划师:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，每个月都是0.6元，固定不变，不会上涨，没有额外费用，你点下立即投保，点了吧\n用户:嗯，\n保险规划师:这边看到您还没提交啊，您就把页面往下滑，然后点右下角的立即投保，点好了嘛？\n用户:呃，不投保可以吗？\n保险规划师:这个就是跟医社保做个补充，把医社保不报的进口药自费药可以给您报了，0.6元也没任何压力，就跟其他客户一样，您放心享有就行好吧？？？\n用户:嗯，\n保险规划师:这个就0.6元没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，点好了吧？\n用户:等我需要的时候，我再投保可以吗？\n保险规划师:这个就是补充医社保的，医社保报销完，剩余的合同约定二次报销，以后医疗费用咱们就不用担心了，每个月就6毛钱不会上涨，现在还可以免费领取价值235元的全身体检卡，您点下立即投保，我带您领取下，点好了吧？？？\n用户:嗯嗯。\n用户:嗯，\n用户:嗯，\n保险规划师:这个保险是生活当中必需品，人人都要有，用不着保的就是平安与健康，万一用着了，就是花个小钱保着大钱的，因为0.6元对我们生活没有任何影响 ，就是一瓶水的钱呢，您点下立即投保，点好了嘛？\n用户:嗯，\n保险规划师:这个就0.6元没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，点好了吧？\n用户:点了。\n用户:(用户没说话)\n保险规划师:点好之后您看下是不是0.6元，是的话您支付下这6毛钱，保障从今晚12点生效，每个月6毛，固定不变，以后大小疾病大小意外只要住院治疗都能保，支付好了告诉我，我给您登记下，（停顿）支付好了吧？\n用户:嗯，\n用户:嗯嗯。\n用户:好。\n用户:嗯，\n保险规划师:这边看到您还没支付啊，您支付下，支付好了告诉我，我帮您登记下领取体检卡，（停顿）支付好了吧？\n用户:等会儿我儿子，我扒拉没了。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "不会操作-家人弄"}]}, {"id": "3ba18740-8d41-11ef-a574-aa940f1afa1a", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:说吧。\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:等不了。\n保险规划师:确实服务非常重要，就耽误您2分钟时间我带您去我们微信公众号上看下好吧？？？\n用户:嗯，看什么。\n保险规划师:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:哦。\n用户:哦，打开了。\n用户:哦。\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:哦。\n用户:哦，你\n用户:总账户\n用户:哦，有公众号点了。\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:哦。\n用户:那个有\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "有康保怎么拼写"}]}, {"id": "3ba19ab4-8d41-11ef-a574-aa940f1afa1a", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:喂。\n用户:说吧。\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:等不了。\n保险规划师:确实服务非常重要，就耽误您2分钟时间我带您去我们微信公众号上看下好吧？？？\n用户:嗯，看什么。\n保险规划师:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:哦。\n用户:哦，打开了。\n用户:哦。\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:哦。\n用户:哦，你\n用户:总账户\n用户:哦，有公众号点了。\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:哦。\n用户:那个有\n保险规划师:然后您点进去，关注下，关注好了之后，在右下角有个我的，您点下，然后第一行有个贵宾通道，看到了吧？\n用户:有没有的，有\n用户:有卡保险是吧。\n保险规划师:这个公众号就是专门服务老用户的，您放心。就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:啊。\n用户:嗯，\n用户:哦，有个有保怎么的。\n用户:关注了，关注了。\n保险规划师:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:关注了\n用户:哦，看到了。\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:九八二六\n保险规划师:您好，您是不是没输好啊，我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:哦。\n用户:怎么他说的什么意外险啊，不是他说什么意外险也不一万了，什么\n保险规划师:是一个医保外的补充医疗，以后医保报完剩下的可以再报一次您输好之后会进入泰康官网页面，泰康惠民健康保，赠全年体检权益，有看到吧\n用户:哦，就一天\n保险规划师:然后您核对下，信息是本人吧？\n用户:这个保费\n保险规划师:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，每个月都是0.6元，固定不变，不会上涨，没有额外费用，你点下立即投保，点了吧\n用户:这这六元一月是\n用户:我不会弄，等他回来吧。"}, {"from": "gpt", "value": "不会操作-家人弄"}]}, {"id": "3ba1b044-8d41-11ef-a574-aa940f1afa1a", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:哎，哪里呀。\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:哎，你讲吧。\n保险规划师:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:你稍等啊，我给你放着，免提上啊。\n保险规划师:抱歉，刚刚没听清，麻烦您再说下？？？\n用户:我放在免提上，你稍等啊。\n保险规划师:（空录音）我带您到我们微信公众号上看下，您打开免提，回到手机主屏幕，然后打开微信，您打开微信和我说一下，我带您操作看一下 （停顿下）您微信打开了吧？\n用户:我我放在免提上，现在放不了啊。\n保险规划师:这个操作挺简单的，就耽误您2分钟时间您看可以吧？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:听到你稍等一下啊，我放免提上，我现在等于\n保险规划师:确实服务非常重要，就耽误您2分钟时间我带您去我们微信公众号上看下好吧？？？\n用户:你说吧。\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:看到了\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:看到了\n保险规划师:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:你这就是一个保险吗？\n保险规划师:这个就是带您去看下保单，免费领取全身体检卡然后您点进去，关注下，关注好了之后，在右下角有个我的，您点下，然后第一行有个贵宾通道，看到了吧？\n用户:看到了\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:好了。\n保险规划师:您输好之后会进入泰康官网页面，泰康惠民健康保，赠全年体检权益，有看到吧\n用户:你这就是一个投保的一个这个产品呢。\n保险规划师:这个就是带您去看下保单，免费领取全身体检卡然后您核对下，信息是本人吧？\n用户:我现在我问你的问题，你没回答我，你这就是一个保险产品，对吗？\n保险规划师:这个就是带您去看下保单，免费领取全身体检卡您看下，信息是不是您本人，没问题吧？\n用户:体检卡我不需要我正常的，我的社保的保险我都不去做体检。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "不需要体检卡"}]}, {"id": "3ba1c7d2-8d41-11ef-a574-aa940f1afa1a", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯，\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险规划师:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:打开了\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n保险规划师:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:这泰康保险\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "无"}]}]
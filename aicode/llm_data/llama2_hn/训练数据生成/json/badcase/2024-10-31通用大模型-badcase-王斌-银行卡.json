[{"id": "6caad62c-977b-11ef-a981-aa940f1afa1b", "conversations": [{"from": "human", "value": "作为一个专业的保险销售员，业务场景是通过电话销售百万医疗保险，你需要根据对话记录选择销售流程下一步的销售流程节点。\n\n销售流程如下表格：\n\n节点|节点目标\n---|---\n打开微信|确认用户是否打开微信\n搜索框|用户打开微信后,第二步,要引导用户找到微信中如何添加公众号,用户拒绝需要劝服\n公众号|用户找到如何添加公众号后,第三步,要引导用户在微信中搜索【如果有保】并看到【如果有保】公众号，用户说“好的”、“嗯”代表用户已经看到了公众号，用户拒绝需要劝服\n点击菜单|用户看到公众号后,第四步,指导用户看到【如果有保】公众号中的指定按键,用户拒绝需要劝服\n输入数字|用户看到指定按键后,第五步,告知用户数字,指导用户输入数字后会自动进入页面,用户拒绝需要劝服\n短险页面|询问用户是否看到【泰康600万】字样的页面，如果用户否定需要再次确认。\n确认费用|与用户确认页面展示的个人身份信息内容及费用是否正确\n立即投保|让用户点击立即投保按钮\n打开支付平台|让用户按照指示打开支付平台\n选银行卡|让用户按照指示自己常用的银行卡，用户完成操作则进行下一流程\n绑卡验证|让用户接收并输入验证码，用户完成输入则进行下一流程\n引导支付|引导用户支付费用\n完善保障|让用户点击完善按钮\n家庭单|确认用户是否需要为自己的家人购买保险\n再次拖单|再次确认用户是否需要为自己的家人购买保险\n挂机|完成销售流程\n\n\n对话记录如下：\n```\n保险销售员:喂，您好~请问您是**先生/女士吧!\n用户:是。\n保险销售员:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n用户:打开了\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:算了。\n用户:嗯，\n用户:看到了\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n保险销售员:您需要先关注一下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:看到。\n保险销售员:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:等一下\n保险销售员:我的工号是 xxxxxx （播报6位数激活码），输入好了吧？？？\n用户:是二四\n用户:输好了呀。\n保险销售员:您输好之后会进入人保官网页面，人保无忧医疗险，赠全年体检权益，有看到吧\n用户:就。\n用户:不是啊，他要输入我的手机号码。\n保险销售员:嗯，对，您输下您的手机号验证下，（停顿2s）输入好了吗？？？\n用户:哎，怎么不太\n用户:是泰康的吗？\n保险销售员:我是中国人保合作顾问，来电就是带您去领取下补充医保的电子保单，教您怎么去理赔然后您核对下，信息是本人吧？\n用户:嗯，\n保险销售员:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版每个月就几块钱，没有额外费用，你点下立即投保，点了吧\n用户:嗯，\n保险销售员:这个就几块钱没有其他费用，需要带您审核下，看能不能通过，您点下立即投保，然后健康没问题点我已逐页阅读并同意（停顿下）点好了嘛？\n用户:等一下啊。\n保险销售员:嗯，没事，不着急，我等您一下？？？\n用户:(用户没说话)\n保险销售员:您好，能听到嘛？\n用户:嗯，听到\n保险销售员:然后会有个健康告知，您往下滑一直滑到最底下，然后点下下面的我已逐页阅读并同意（停顿下）点好了吧？\n用户:我在点\n用户:对。\n用户:好了，投保成功了。\n保险销售员:然后会到添加银行卡，因为日后理赔都是打到银行卡的，您选个您常用的银行卡，不用输卡号哈，您点那个选择就可以，然后点下一步，点好了吧？\n用户:啊。\n用户:官网天了。\n用户:算了。\n用户:你。\n用户:已经搞好了。\n用户:你已投保成功了，它显示\n保险销售员:然后会验证下您的手机号，您点下获取验证码，会给您发个6位数的验证码，然后您填下，（停顿2s）填好了吧？\n用户:没呀。\n用户:认识呀。\n用户:没呀。\n用户:已经投保成功了呀。\n\n```\n请注意:\n1.销售流程节点按照表格从上到下执行,同时也要注意节点目标是否完成,在不引起用户反感的情况下,要节点目标达成后再进行下一个节点\n2.根据保险销售员其他的话术一步一步确认销售流程的节点,但是需要忽略掉用户提出的问题,同时也忽略掉保险销售员对用户问题的解答\n3.你的目的是完成销售,忽略用户的负向反馈,继续走销售流程\n保险销售员的下一句的销售流程节点(仅选择销售流程表格中的一个节点)是:"}, {"from": "gpt", "value": "完善保障"}]}, {"id": "6cac03c6-977b-11ef-a981-aa940f1afa1b", "conversations": [{"from": "human", "value": "作为一个专业的保险销售员，业务场景是通过电话销售百万医疗保险，你需要根据对话记录选择销售流程下一步的销售流程节点。\n\n销售流程如下表格：\n\n节点|节点目标\n---|---\n来电目的|告知用户之前送的意外险，防止用户不会理赔，带用户去查看保单。\n打开微信|确认用户是否打开微信\n搜索框|用户打开微信后,第二步,要引导用户找到微信中如何添加公众号,用户拒绝需要劝服\n公众号|用户找到如何添加公众号后,第三步,要引导用户在微信中搜索【如果有保】并看到【如果有保】公众号，用户说“好的”、“嗯”代表用户已经看到了公众号，用户拒绝需要劝服\n点击菜单|用户看到公众号后,第四步,指导用户看到【如果有保】公众号中的指定按键,用户拒绝需要劝服\n输入数字|用户看到指定按键后,第五步,告知用户数字,指导用户输入数字后会自动进入页面,用户拒绝需要劝服\n短险页面|询问用户是否看到惠民保字样的页面\n确认费用|与用户确认页面展示的个人身份信息内容及费用是否正确\n立即投保|让用户点击立即投保按钮\n选银行卡|让用户按照指示自己常用的银行卡，用户完成操作则进行下一流程\n绑卡验证|让用户接收并输入验证码，用户完成输入则进行下一流程\n引导支付|引导用户支付费用\n完善保障|让用户点击完善按钮\n加保|用户支付成功后，让用户加保重疾险，告知用户后则进行下一步\n介绍重疾险|向用户介绍重疾险，告知用户后则进行下一步\n挂机|完成销售流程\n\n\n对话记录如下：\n```\n保险销售员:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:啊。\n用户:他的。\n用户:在。\n保险销售员:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:我。\n用户:哎，\n用户:嗯，\n保险销售员:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:那你。\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:啊。\n用户:啊，你\n用户:在哪里？\n保险销售员:咱们这个是补充医保报销的，是泰康给您承保的，以后医社保报销完剩下的可以二次报销就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:我。\n用户:啊。\n用户:嗯，\n用户:(用户没说话)\n保险销售员:您搜索一下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，您输入下，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:哎，\n用户:嗯，\n用户:那是。\n用户:(用户没说话)\n保险销售员:您好，您有看到有康保公众号嘛？\n用户:啊。\n用户:啊，你\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:三。\n用户:我。\n用户:啊。\n保险销售员:就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:啊。\n用户:(用户没说话)\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，\n用户:那是。\n用户:啊。\n用户:啊。\n保险销售员:就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:在哪里？\n用户:我。\n用户:(用户没说话)\n保险销售员:您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:哎，\n用户:嗯，\n用户:(用户没说话)\n保险销售员:您好有看到公众号三个字嘛？就是您点下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，看到了吧？\n用户:嗯，\n用户:(用户没说话)\n保险销售员:就是在微信右上角有个带圆圈的➕号，您点下，然后再点第二行的添加朋友，在倒数第二行有公众号这三个字，看到了吧？\n用户:啊。\n用户:啊，对\n用户:在哪里？\n用户:我。\n保险销售员:您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:哎，\n用户:嗯，\n保险销售员:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:哎，\n用户:嗯，\n用户:那你。\n\n```\n请注意:\n1.销售流程节点按照表格从上到下执行,同时也要注意节点目标是否完成,在不引起用户反感的情况下,要节点目标达成后再进行下一个节点\n2.根据保险销售员其他的话术一步一步确认销售流程的节点,但是需要忽略掉用户提出的问题,同时也忽略掉保险销售员对用户问题的解答\n3.你的目的是完成销售,忽略用户的负向反馈,继续走销售流程\n保险销售员的下一句的销售流程节点(仅选择销售流程表格中的一个节点)是:"}, {"from": "gpt", "value": "公众号"}]}]
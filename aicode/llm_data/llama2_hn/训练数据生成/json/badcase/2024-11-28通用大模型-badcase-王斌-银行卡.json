[{"id": "27effe52-ad6c-11ef-a77d-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:是吧。\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:好的。\n保险规划师:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:是吧，继续\n用户:嗯，\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:你说。\n用户:你说继续\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:就干嘛，公众号\n保险规划师:就是带您去我们公众号上看下您之前的保单就是您搜索下“有康保”，有是有没有的有，康是健康的康，保是保险的保，有康保，然后会有个有康保的公众号，看到了吧？\n用户:行看。\n用户:看到了\n用户:谢谢谢，谢谢，找到了，找到了，找到了\n用户:看到了\n保险规划师:您需要先关注一下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:我的。\n用户:你好。\n保险规划师:您关注下“有康保”公众号，关注好了之后，在右下角有个我的，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:还有个转输工号。\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报6位数激活码），您输入下，输入好了吧？\n用户:好了。\n保险规划师:您输好之后会进入泰康官网页面，泰康惠民健康保，赠全年体检权益，有看到吧\n用户:对，有啊。\n保险规划师:然后您核对下，信息是本人吧？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:哎，被保人姓名，我的手手显示姓名吗？不是。\n保险规划师:那可能是系统信息有误，没关系，您可以改成您的信息那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，基础版一天0.02元，一个月0.6元，没有额外费用，你点下立即投保，点了吧\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？？？\n用户:可以的\n用户:能听到可以的\n保险规划师:这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础版一天0.02元，一个月0.6元，没有额外费用，您点下立即投保，点了吗？\n用户:他他，他不能，他不能，他不能用那个嘛，不能用那个微信支付是吧。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "微信可以投保吗"}]}, {"id": "27f01478-ad6c-11ef-a77d-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:是在哪里？\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:医保。\n用户:医保。\n用户:一。\n用户:你是什么公司？\n保险规划师:我们是泰康保险，泰康是世界五百钱企业，中国前四大保险公司，今天就是带您去看下补充医保的电子保单然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着惠民健康保，月月0.6，最高可赔600万，看到了吧？\n用户:什么公司？\n保险规划师:我是泰康的，就是带您去看下补充医保的保单那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，每个月都是0.6元，固定不变，不会上涨，没有额外费用，您往下滑看下左下角的费用是0.6元吧？\n用户:哦。\n用户:哦，我我之前有买过吗？\n保险规划师:这个您还没保成功呢，需要我带您去领取下保单，日后才可以报销理赔这个是医保报销完的，剩余的合同约定的我们都给您报销，每个月都是0.6元，固定不变不会上涨，没有额外费用，您点下立即投保，点了吗？\n用户:哦。\n用户:哦。\n用户:哦，怎么操作。\n用户:怎么操作？\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "然后怎么操作"}]}, {"id": "27f024cc-ad6c-11ef-a77d-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:是在哪里？\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:医保。\n用户:医保。\n用户:一。\n用户:你是什么公司？\n保险规划师:我们是泰康保险，泰康是世界五百钱企业，中国前四大保险公司，今天就是带您去看下补充医保的电子保单然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着惠民健康保，月月0.6，最高可赔600万，看到了吧？\n用户:什么公司？\n保险规划师:我是泰康的，就是带您去看下补充医保的保单那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，每个月都是0.6元，固定不变，不会上涨，没有额外费用，您往下滑看下左下角的费用是0.6元吧？\n用户:哦。\n用户:哦，我我之前有买过吗？\n保险规划师:这个您还没保成功呢，需要我带您去领取下保单，日后才可以报销理赔这个是医保报销完的，剩余的合同约定的我们都给您报销，每个月都是0.6元，固定不变不会上涨，没有额外费用，您点下立即投保，点了吗？\n用户:哦。\n用户:哦。\n用户:哦，怎么操作。\n用户:怎么操作？\n保险规划师:您就把医院的单据发票都留好，到时候拨打泰康客服热线95522就可以了这个保险是生活当中必需品，人人都要有，用不着保的就是平安与健康，万一用着了，就是花个小钱保着大钱的，因为0.6元对我们生活没有任何影响 ，就是一瓶水的钱呢，您点下立即投保，点好了嘛？\n用户:在哪里？点\n用户:不是我在哪里买呀。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "没找到立即投保按扭"}]}, {"id": "27f02ce2-ad6c-11ef-a77d-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:是在哪里？\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:医保。\n用户:医保。\n用户:一。\n用户:你是什么公司？\n保险规划师:我们是泰康保险，泰康是世界五百钱企业，中国前四大保险公司，今天就是带您去看下补充医保的电子保单然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着惠民健康保，月月0.6，最高可赔600万，看到了吧？\n用户:什么公司？\n保险规划师:我是泰康的，就是带您去看下补充医保的保单那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，每个月都是0.6元，固定不变，不会上涨，没有额外费用，您往下滑看下左下角的费用是0.6元吧？\n用户:哦。\n用户:哦，我我之前有买过吗？\n保险规划师:这个您还没保成功呢，需要我带您去领取下保单，日后才可以报销理赔这个是医保报销完的，剩余的合同约定的我们都给您报销，每个月都是0.6元，固定不变不会上涨，没有额外费用，您点下立即投保，点了吗？\n用户:哦。\n用户:哦。\n用户:哦，怎么操作。\n用户:怎么操作？\n保险规划师:您就把医院的单据发票都留好，到时候拨打泰康客服热线95522就可以了这个保险是生活当中必需品，人人都要有，用不着保的就是平安与健康，万一用着了，就是花个小钱保着大钱的，因为0.6元对我们生活没有任何影响 ，就是一瓶水的钱呢，您点下立即投保，点好了嘛？\n用户:不是我在哪里买你的会员。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "没找到立即投保按扭"}]}, {"id": "27f035f2-ad6c-11ef-a77d-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:是在哪里？\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:医保。\n用户:医保。\n用户:一。\n用户:你是什么公司？\n保险规划师:我们是泰康保险，泰康是世界五百钱企业，中国前四大保险公司，今天就是带您去看下补充医保的电子保单然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着惠民健康保，月月0.6，最高可赔600万，看到了吧？\n用户:什么公司？\n保险规划师:我是泰康的，就是带您去看下补充医保的保单那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，每个月都是0.6元，固定不变，不会上涨，没有额外费用，您往下滑看下左下角的费用是0.6元吧？\n用户:哦。\n用户:哦，我我之前有买过吗？\n保险规划师:这个您还没保成功呢，需要我带您去领取下保单，日后才可以报销理赔这个是医保报销完的，剩余的合同约定的我们都给您报销，每个月都是0.6元，固定不变不会上涨，没有额外费用，您点下立即投保，点了吗？\n用户:哦。\n用户:哦。\n用户:哦，怎么操作。\n用户:怎么操作？\n保险规划师:您就把医院的单据发票都留好，到时候拨打泰康客服热线95522就可以了这个保险是生活当中必需品，人人都要有，用不着保的就是平安与健康，万一用着了，就是花个小钱保着大钱的，因为0.6元对我们生活没有任何影响 ，就是一瓶水的钱呢，您点下立即投保，点好了嘛？\n用户:在哪里交呢？你这里是电子，是吧，你这里是电子版，是吧。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "没找到立即投保按扭"}]}, {"id": "27f0465a-ad6c-11ef-a77d-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:好。\n保险规划师:您好，您电话总算接通了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:到医保啊。\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:医保都交完了吗？\n用户:还没收到呢。\n保险规划师:您稍等下，可能有些延迟，您不要挂电话，打开免提，应该给您发过去了，好效保开头的，看到了吧？？？\n用户:你发。\n用户:啊。\n用户:没看到那你发到那个我儿媳妇那个手机里了吧。\n保险规划师:发您手机短信上了，是刚刚给您发的，好效保开头的，您看下，有吗？？？\n用户:你发哪个点上了？\n用户:你那个那个手机尾号是啥号，哪个\n保险规划师:就是发跟您通话的这个手机号上了，刚刚给您发的，好效保开头的，您看下收到没？？？\n用户:嗯，\n用户:哦。\n用户:嗯，那我没，我没搁家。\n保险规划师:然后上面有个蓝色英文字母链接，您点下，点开之后您应该会看到一个页面写着惠民健康保，月月0.6，最高可赔600万，看到了吧？\n用户:我搁外边儿呢。\n用户:啊。\n用户:哎呀。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "不方便操作"}]}]
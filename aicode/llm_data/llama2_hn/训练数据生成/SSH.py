# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2024/2/18 15:00
import os
import re
from loguru import logger
import paramiko
from scp import SCPClient

current_path = os.path.abspath(__file__)
current_dir = os.path.dirname(current_path)
parent_dir = os.path.dirname(current_dir)
grand_parent_dir = os.path.dirname(parent_dir)
private_key_path = f"{grand_parent_dir}/temp/id_rsa"
private_key = paramiko.RSAKey.from_private_key_file(private_key_path)


def upload(ip, port, username, source_file, target_dir):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh.connect(ip, port, username, pkey=private_key)

    scp = SCPClient(ssh.get_transport())
    scp.put(source_file, target_dir)
    scp.close()

    ssh.close()


def sed_replace_date(ip, filename, port=22, username='root', script_name='llm_train.sh'):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh.connect(ip, port, username, pkey=private_key)

    pattern = r'[0-9]{4}-[0-9]{2}-[0-9]{2}'
    match = re.search(pattern, filename)
    if not match:
        return
    datestr = match.group()
    stdin, stdout, stderr = ssh.exec_command("sed -i -E 's/[0-9]{4}-[0-9]{2}-[0-9]{2}/" + datestr + "/g' /llm/lishuaichao/fc/scripts/" + script_name)
    output = stdout.read()
    print(output)
    datestr = datestr.replace('-', '')
    stdin, stdout, stderr = ssh.exec_command("sed -i -E 's/_[0-9]{4}[0-9]{2}[0-9]{2}_/_" + datestr + "_/g' /llm/lishuaichao/fc/scripts/" + script_name)
    output = stdout.read()
    print(output)
    stdin, stdout, stderr = ssh.exec_command("sed -i -E 's/[0-9]{4}[0-9]{2}[0-9]{2}/" + datestr + "/g' /llm/lishuaichao/fc/restart.sh")
    output = stdout.read()
    stdin, stdout, stderr = ssh.exec_command("sed -i -E 's/[0-9]{4}[0-9]{2}[0-9]{2}/" + datestr + "/g' /llm/lishuaichao/fc/restart.sh-67.sh")
    output = stdout.read()
    print(output)

    ssh.close()
    logger.info('修改服务器上的训练脚本成功,ip:{},训练数据文件:{}', ip, filename)

def sed_replace_date_release(ip, filename, port=22, username='root', script_name='llm_train_release.sh', script_name2='restart_release.sh'):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh.connect(ip, port, username, pkey=private_key)

    pattern = r'[0-9]{4}-[0-9]{2}-[0-9]{2}'
    match = re.search(pattern, filename)
    if not match:
        return
    datestr = match.group()
    stdin, stdout, stderr = ssh.exec_command("sed -i -E 's/[0-9]{4}-[0-9]{2}-[0-9]{2}/" + datestr + "/g' /llm/lishuaichao/fc/scripts/" + script_name)
    output = stdout.read()
    print(output)
    datestr = datestr.replace('-', '')
    stdin, stdout, stderr = ssh.exec_command("sed -i -E 's/_[0-9]{4}[0-9]{2}[0-9]{2}_/_" + datestr + "_/g' /llm/lishuaichao/fc/scripts/" + script_name)
    output = stdout.read()
    stdin, stdout, stderr = ssh.exec_command("sed -i -E 's/[0-9]{4}[0-9]{2}[0-9]{2}/" + datestr + "/g' /llm/lishuaichao/fc/" + script_name2)
    output = stdout.read()
    print(output)

    ssh.close()
    logger.info('修改服务器上的训练脚本成功,ip:{},训练数据文件:{}', ip, filename)



def upload_json(ip, filename, username='root', port=22, target_dir='/llm/lishuaichao/fc/data/'):
    source_file = f'{parent_dir}/训练数据生成/output/{filename}'
    upload(ip, port, username, source_file, target_dir)
    logger.info('上传json文件到训练服务器成功,ip:{},json名称:{}', ip, filename)

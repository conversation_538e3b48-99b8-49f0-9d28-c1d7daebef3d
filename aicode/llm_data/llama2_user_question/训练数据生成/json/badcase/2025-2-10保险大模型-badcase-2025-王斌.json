[{"id": "b567e2dc-e795-11ef-a63d-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:支付好之后点完成，然后会看到恭喜您投保成功，下面有个贵宾限时专享活动，完善重疾保障，看到了吧？\n用户:怎么是一百一百五十八一个月呀。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "看到重疾险月费用"}]}, {"id": "b5680802-e795-11ef-a63d-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:支付好之后点完成，然后会看到恭喜您投保成功，下面有个贵宾限时专享活动，完善重疾保障，看到了吧？\n用户:我什么\n保险规划师:然后您点一下那个完善重疾保障，对，点完之后有没有看到泰爱保重疾险，看到了吧？\n用户:嗯，看到了。\n保险规划师:他这个是先给钱后看病的，就是以后生病了，在住院之前就可以先拿钱，只要确诊合同约定疾病最高就能拿60万，这个钱就是赔给你的，你想怎么花怎么花，看好病之后你把发票单据留好，这边该报多少还能再报一次，也就说一前一后可以赔两次，多的钱也不用退，您自由支配，明白吧？\n用户:嗯，\n保险规划师:然后您把页面往下滑，滑到最底下，看下左下角的费用是不是几块钱啊，是几块钱吧？\n用户:\n保险规划师:您好，能听到嘛？\n用户:哪里啊，没有啊。\n保险规划师:就是您页面一直往下滑，在左下角就有个费用，看到了吧？？？\n用户:没有啊。\n用户:三块多，每个月三块多，不是六毛吗？\n保险规划师:那您也是享受到了这个补贴减免，相当于老客户打了5折，然后您点下右下角的立即投保，点好了吧？\n用户:是每个月那么多，还是这这个月下个月开始加\n保险规划师:这个每个月费用固定不变，不会上涨，您放心就行，你点一下那个立即投保，我带您去完善下，点好了吧？？？\n用户:什么一百块什么意思啊。\n保险规划师:这个是一个月费用，一天几块钱，一瓶矿泉水的钱，也是做了个减免，交完这个就没有了那您也是享受到了这个补贴减免，相当于老客户打了5折，然后您点下右下角的立即投保，点好了吧？\n用户:这样了。\n保险规划师:然后会到添加银行卡，因为日后理赔都是打到银行卡的，您选个您常用的银行卡，不用输卡号哈，您点那个选择就可以，然后点下一步，这个交完就结束了哈，点好了吧？\n用户:现在。\n用户:那我现在不想交了，要交一百块钱，不想交了怎么退出来。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "不需要重疾险-费用高"}]}, {"id": "b5681e50-e795-11ef-a63d-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:支付好之后点完成，然后会看到恭喜您投保成功，下面有个贵宾限时专享活动，完善重疾保障，看到了吧？\n用户:没有，我刚刚退出来了，投保了，但是我先退出来了，怎么弄怎么弄回去。\n保险规划师:那也没关系哈，可以把这个名额给您家人，我先带您去看下然后您点一下那个完善重疾保障，对，点完之后有没有看到泰爱保重疾险，看到了吧？\n用户:在哪里啊？\n保险规划师:就是投保完成下面不是有个完善重疾保障按钮嘛，看到了吧？然后您点一下那个完善重疾保障，对，点完之后有没有看到泰爱保重疾险，看到了吧？\n用户:没有啊。\n保险规划师:就是投保完成下面不是有个完善重疾保障按钮嘛，看到了吧？然后您点一下那个完善重疾保障，对，点完之后有没有看到泰爱保重疾险，看到了吧？\n用户:不是我完了，付完了，刚付完退出来了，不小心退出来了。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "关闭了短险页面"}]}, {"id": "b5683408-e795-11ef-a63d-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:您好，您终于接电话了，首先祝您新年快乐，您的城乡结合医疗保障已经到账了，但是保单还没收到，这样日后就没办法理赔了，来电是带您去看下保单，以后进口药自费药都能100%报销了，2分钟时间我给您讲解下好吧？\n用户:你是谁呀？我们又没有搞什么保单。\n保险规划师:就是补充咱们国家的医社保新农合，把社保不报的给您报了确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:你怎么？你\n用户:你怎么知道这个号码是我们这个村的头疼告诉你的吗？\n保险规划师:这个您可以放心，这个是我们这次活动的通知电话，后期需要咨询理赔可以拨打泰康客服热线95522，这个您放心就行您稍等下，可能有些延迟，您不要挂电话，打开免提，应该给您发过去了，好效保开头的，看到了吧？\n用户:嗯，\n用户:嗯，但\n用户:我母我母亲，她现在刚去买东西，等他回来太忙，我不知道是多少。\n保险规划师:那也没关系哈，比较简单，您可以替他看下这是免费给您赠送的，全家人都可以免费领，保额高达100万，是完全免费的，我带您去看下保单，您看下好效保开头的短信收到了吗？\n用户:你。\n用户:嗯，你有我收到了。\n保险规划师:那您点开短信上的蓝色英文字母链接，点开就能进入泰康官网页面，泰无忧意外险，100万，一张保单保全家看到了吧？？？\n用户:我跟他不是刚刚跟你阿姨，我刚好不是刚刚跟你说我妈懂了，我不知道密码是多少，不不会打开，然后我还怎么看啊。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "家人接听电话"}]}, {"id": "b56847cc-e795-11ef-a63d-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:您好，您终于接电话了，首先祝您新年快乐，您的城乡结合医疗保障已经到账了，但是保单还没收到，这样日后就没办法理赔了，来电是带您去看下保单，以后进口药自费药都能100%报销了，2分钟时间我给您讲解下好吧？\n用户:你是谁呀？我们又没有搞什么保单。\n保险规划师:就是补充咱们国家的医社保新农合，把社保不报的给您报了确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:你怎么？你\n用户:你怎么知道这个号码是我们这个村的头疼告诉你的吗？\n保险规划师:这个您可以放心，这个是我们这次活动的通知电话，后期需要咨询理赔可以拨打泰康客服热线95522，这个您放心就行您稍等下，可能有些延迟，您不要挂电话，打开免提，应该给您发过去了，好效保开头的，看到了吧？\n用户:嗯，\n用户:嗯，但\n用户:我母我母亲，她现在刚去买东西，等他回来太忙，我不知道是多少。\n保险规划师:那也没关系哈，比较简单，您可以替他看下这是免费给您赠送的，全家人都可以免费领，保额高达100万，是完全免费的，我带您去看下保单，您看下好效保开头的短信收到了吗？\n用户:你。\n用户:嗯，你有我收到了。\n保险规划师:那您点开短信上的蓝色英文字母链接，点开就能进入泰康官网页面，泰无忧意外险，100万，一张保单保全家看到了吧？？？\n用户:我跟他不是刚刚跟你阿姨，我刚好不是刚刚跟你说我妈懂了，我不知道密码是多少，不不会打开，然后我还怎么看啊。\n保险规划师:就是短信上不是有个蓝色英文字母链接嘛，您点下，然后有个访问或者打开，您打开下就能看到，泰无忧意外险，100万，一张保单保全家，看到了吧？？？\n用户:嗯，好\n保险规划师:这是限时免费给您赠送的，一张保单全家人都保了，需要验证下是您本人操作，验证下手机号，您点下获取验证码，填下验证码，您填下（停顿下）填好了吧？\n用户:阿姨，你又不说啊。\n用户:嗯，\n用户:我跟你，我跟你\n用户:阿姨，我跟你说，我妈出去的手机，手机了，我不知道你是多少，打不开，还是我怎么用啊。\n保险规划师:那也没关系哈，比较简单，您可以替他看下这是咱们今年唯一可以免费领的全家保障了，错过就没有了，您获取下验证码，会给您发个4位数的验证码，您填下（停顿下）填好了吧？\n用户:那我电话打给你。\n用户:我知道啊。\n用户:密码被打错了，我不知道怎么开通，密码被我慢慢锁定了，我不知道怎么开，它也没有告诉过完了我怎么看啊。\n保险规划师:这个就是带您去看下保障内容，告诉您怎么理赔这是限时免费给您赠送的，一张保单全家人都保了，需要验证下是您本人操作，验证下手机号，您点下获取验证码，填下验证码，您填下（停顿下）填好了吧？\n用户:哦。\n用户:那个你们，你们你们是哪里啊？\n保险规划师:是泰康保险给您承保的，泰康是世界五百强企业，中国前四大保险公司，今天就是带您去看下补充医保的电子保单这是咱们今年唯一可以免费领的全家保障了，错过就没有了，您获取下验证码，会给您发个4位数的验证码，您填下（停顿下）填好了吧？\n用户:嗯，\n用户:嗯，\n用户:嗯，\n用户:填好了\n保险规划师:因为日后理赔都是实名认证的，您输下您的姓名身份证号，这个不能有错哈，不然就没办法理赔报销了，您填下（停顿下）您信息填好了嘛？\n用户:这个身份证号我不是\n用户:这个身份证号我不知道啊。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "不记得投保信息"}]}]
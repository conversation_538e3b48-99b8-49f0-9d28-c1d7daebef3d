[{"id": "ab29db5e-f35c-11ef-a63e-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯，那我呀。\n保险规划师:您好，您终于接电话了，首先祝您新年快乐，您的城乡结合医疗保障已经到账了，但是保单还没收到，这样日后就没办法理赔了，来电是带您去看下保单，以后进口药自费药都能100%报销了，2分钟时间我给您讲解下好吧？\n用户:嗯，好你说吧。\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:哦，没有不好意思。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "没收到短信"}]}, {"id": "ab29e40a-f35c-11ef-a63e-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯，\n保险规划师:您好，您终于接电话了，首先祝您新年快乐，您的城乡结合医疗保障已经到账了，但是保单还没收到，这样日后就没办法理赔了，来电是带您去看下保单，以后进口药自费药都能100%报销了，2分钟时间我给您讲解下好吧？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？\n用户:你说啊。\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:没有。\n保险规划师:您稍等下，可能有些延迟，您不要挂电话，打开免提，应该给您发过去了，好效保开头的，看到了吧？？？\n用户:没有近期到啊。\n保险规划师:是不是您手机拦截了啊，您看下拦截或者未读短信，屏蔽里有没有？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？\n用户:没有没有\n用户:没有。\n保险规划师:那这样我带您去微信公众号上看下，您打开免提，然后回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:不用。\n保险规划师:这个您别着急拒绝我嘛，这个就是给您的保单做个升级，保障更加全面了，大小疾病大小意外全部保障到了，进口药自费药都能报销，我先带您去看下好吧？？？\n用户:这是什么电话？\n保险规划师:咱们这个是补充医保报销的，是泰康给您承保的，以后医社保报销完剩下的可以二次报销您好，您微信打开了吗，我带您去我们官方公众号上看下保单（停顿下）微信打开了吧？\n用户:知道在哪里。\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:这个有什么\n保险规划师:您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:我都银行卡这个\n用户:这个信息是不是好多年前在网上\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "没投保怎么有我身份信息"}]}, {"id": "ab29ecde-f35c-11ef-a63e-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯，\n保险规划师:您好，您终于接电话了，首先祝您新年快乐，您的城乡结合医疗保障已经到账了，但是保单还没收到，这样日后就没办法理赔了，来电是带您去看下保单，以后进口药自费药都能100%报销了，2分钟时间我给您讲解下好吧？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？\n用户:你说啊。\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:没有。\n保险规划师:您稍等下，可能有些延迟，您不要挂电话，打开免提，应该给您发过去了，好效保开头的，看到了吧？？？\n用户:没有近期到啊。\n保险规划师:是不是您手机拦截了啊，您看下拦截或者未读短信，屏蔽里有没有？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？\n用户:没有没有\n用户:没有。\n保险规划师:那这样我带您去微信公众号上看下，您打开免提，然后回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:不用。\n保险规划师:这个您别着急拒绝我嘛，这个就是给您的保单做个升级，保障更加全面了，大小疾病大小意外全部保障到了，进口药自费药都能报销，我先带您去看下好吧？？？\n用户:这是什么电话？\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "为什么打电话"}]}, {"id": "ab29f30a-f35c-11ef-a63e-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯，\n保险规划师:您好，您终于接电话了，首先祝您新年快乐，您的城乡结合医疗保障已经到账了，但是保单还没收到，这样日后就没办法理赔了，来电是带您去看下保单，以后进口药自费药都能100%报销了，2分钟时间我给您讲解下好吧？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？\n用户:你说啊。\n保险规划师:确保您的权益，我把保单详情发给您了，您看下有没有收到我们的官方短信，好效保开头的，您看下收到了吧？\n用户:没有。\n保险规划师:您稍等下，可能有些延迟，您不要挂电话，打开免提，应该给您发过去了，好效保开头的，看到了吧？？？\n用户:没有近期到啊。\n保险规划师:是不是您手机拦截了啊，您看下拦截或者未读短信，屏蔽里有没有？？？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？\n用户:没有没有\n用户:没有。\n保险规划师:那这样我带您去微信公众号上看下，您打开免提，然后回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:不用。\n保险规划师:这个您别着急拒绝我嘛，这个就是给您的保单做个升级，保障更加全面了，大小疾病大小意外全部保障到了，进口药自费药都能报销，我先带您去看下好吧？？？\n用户:这是什么电话？\n保险规划师:咱们这个是补充医保报销的，是泰康给您承保的，以后医社保报销完剩下的可以二次报销您好，您微信打开了吗，我带您去我们官方公众号上看下保单（停顿下）微信打开了吧？\n用户:知道在哪里。\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:这个有什么\n保险规划师:您点击一下微信右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，请问您这边有看到吗？\n用户:我都银行卡这个\n用户:这个信息是不是好多年前在网上\n保险规划师:这个您还没保成功呢，需要我带您去领取下保单，日后才可以报销理赔您好有看到公众号三个字嘛？就是您点下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，看到了吧？\n用户:点开的那个\n用户:嗯，\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“好效健康”，好是好不好的好，效是效果的效，效率的效，健康就是身体健康的健康，好效健康您输入下，输好之后点下搜索，然后下方会出现好效健康的公众号，看到了吧？\n用户:他这个我\n用户:嗯，\n保险规划师:您关注下“好效健康”公众号，关注好了之后，在左下角有个保险定制，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:你什么我点不出来。\n保险规划师:没事，不着急，我等您一下（停顿3s）好了吧？？？\n用户:我不知道这个是\n保险规划师:就是关注好效健康公众号，然后点左下角的保险定制，然后第一行有个贵宾通道看到了吧？？？\n用户:是真的还是假的。\n保险规划师:这个肯定是真的啊，我带您去看下具体的保障内容您就清楚了然后您点进去，关注下，关注好了之后，在左下角有个保险定制，您点下，然后第一行有个贵宾通道，看到了吧？\n用户:你那个账号，那里\n保险规划师:我们的专属公众号是好效健康公众号就是在左下角有个保险定制，点下，然后第一行是贵宾通道，然后会出现泰康的页面，写着请输入专属工号看到了吧？\n用户:(用户没说话)\n保险规划师:您好，能听到嘛？\n用户:听到，但是我找不到\n保险规划师:就在好效健康公众号里边，点左下角的保险定制，然后点贵宾通道您需要先关注一下“好效健康”公众号，关注好了之后，在左下角有个保险定制，您点下，然后第一行有贵宾通道四个字，看到没？\n用户:呃，我微信那里啊。\n用户:在微信那里，还是在转\n用户:(用户没说话)\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "页面打不开"}]}]
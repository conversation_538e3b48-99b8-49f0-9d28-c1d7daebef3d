[{"id": "bd834ce8-e53f-11ef-83ae-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:支付好之后点完成，然后会看到恭喜您投保成功，下面有个贵宾限时专享活动，完善重疾保障，看到了吧？\n用户:已经支付成功签签订快\n保险规划师:然后您点一下那个完善重疾保障，对，点完之后有没有看到泰爱保重疾险，看到了吧？\n用户:快捷支付\n用户:嗯，\n保险规划师:他这个是先给钱后看病的，就是以后生病了，在住院之前就可以先拿钱，只要确诊合同约定疾病最高就能拿36万，这个钱就是赔给你的，你想怎么花怎么花，看好病之后你把发票单据留好，这边该报多少还能再报一次，也就说一前一后可以赔两次，多的钱也不用退，您自由支配，明白吧？\n用户:嗯，\n保险规划师:然后您把页面往下滑，滑到最底下，看下左下角的费用是不是几块钱啊，是几块钱吧？\n用户:嗯，看一下。\n保险规划师:好的，不着急，您操作下那您也是享受到了这个补贴减免，相当于老客户打了5折，然后您点下右下角的立即投保，点好了吧？\n用户:那你这个写的是一点零三元一天。\n保险规划师:那您也是享受到了这个补贴减免，相当于老客户打了5折，然后您点下右下角的立即投保，点好了吧？\n用户:嗯，\n保险规划师:然后会到添加银行卡，因为日后理赔都是打到银行卡的，您选个您常用的银行卡，不用输卡号哈，您点那个选择就可以，然后点下一步，这个交完就结束了哈，点好了吧？\n用户:不是你这个东西跟你说的不一样。\n用户:不是你这个都不一样，那个上面写的是\n保险规划师:您看到费用就是您的然后会到添加银行卡，因为日后理赔都是打到银行卡的，您选个您常用的银行卡，不用输卡号哈，您点那个选择就可以，然后点下一步，这个交完就结束了哈，点好了吧？\n用户:三十一元一个月。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "看到重疾险月费用"}]}, {"id": "bd8352a6-e53f-11ef-83ae-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:支付好之后点完成，然后会看到恭喜您投保成功，下面有个贵宾限时专享活动，完善重疾保障，看到了吧？\n用户:嗯，\n用户:嗯嗯。\n保险规划师:然后您点一下那个完善重疾保障，对，点完之后有没有看到泰爱保重疾险，看到了吧？\n用户:你看了。\n保险规划师:他这个是先给钱后看病的，就是以后生病了，在住院之前就可以先拿钱，只要确诊合同约定疾病最高就能拿36万，这个钱就是赔给你的，你想怎么花怎么花，看好病之后你把发票单据留好，这边该报多少还能再报一次，也就说一前一后可以赔两次，多的钱也不用退，您自由支配，明白吧？\n用户:嗯，就是我看病就直接联系你这个保险公司就行了，是吧。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "怎么报销"}]}, {"id": "bd8356e8-e53f-11ef-83ae-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:支付好之后点完成，然后会看到恭喜您投保成功，下面有个贵宾限时专享活动，完善重疾保障，看到了吧？\n用户:嗯，\n用户:嗯嗯。\n保险规划师:然后您点一下那个完善重疾保障，对，点完之后有没有看到泰爱保重疾险，看到了吧？\n用户:你看了。\n保险规划师:他这个是先给钱后看病的，就是以后生病了，在住院之前就可以先拿钱，只要确诊合同约定疾病最高就能拿36万，这个钱就是赔给你的，你想怎么花怎么花，看好病之后你把发票单据留好，这边该报多少还能再报一次，也就说一前一后可以赔两次，多的钱也不用退，您自由支配，明白吧？\n用户:嗯，就是我看病就直接联系你这个保险公司就行了，是吧。\n保险规划师:我们的全国客服热线是95522然后您把页面往下滑，滑到最底下，看下左下角的费用是不是几块钱啊，是几块钱吧？\n用户:嗯，\n用户:嗯，\n保险规划师:那您也是享受到了这个补贴减免，相当于老客户打了5折，然后您点下右下角的立即投保，点好了吧？\n用户:三块，三块，四\n用户:嗯，对\n用户:嗯，\n保险规划师:然后会到添加银行卡，因为日后理赔都是打到银行卡的，您选个您常用的银行卡，不用输卡号哈，您点那个选择就可以，然后点下一步，这个交完就结束了哈，点好了吧？\n用户:这是每个月交还是说\n保险规划师:这个是按月交的，交多久保多久您自己定，最高续保到99岁然后会到添加银行卡，因为日后理赔都是打到银行卡的，您选个您常用的银行卡，不用输卡号哈，您点那个选择就可以，然后点下一步，这个交完就结束了哈，点好了吧？\n用户:一年的\n用户:我，我\n用户:我我，我现在交的这个\n用户:我现在觉得这个是保保保一个月的吗？\n保险规划师:这个费用按月缴，基础版优惠完之后每个月都是几块钱，然后交多久保多久您自己定，不做捆绑然后会到添加银行卡，因为日后理赔都是打到银行卡的，您选个您常用的银行卡，不用输卡号哈，您点那个选择就可以，然后点下一步，这个交完就结束了哈，点好了吧？\n用户:不是不是不是。\n用户:等一下等一下等一下。\n保险规划师:嗯，不着急，您看下？？？\n用户:你你这个\n用户:你你你这个一百零九一百零一点九一个月，是什么意思？\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "看到重疾险月费用"}]}, {"id": "bd836372-e53f-11ef-83ae-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:支付好之后点完成，然后会看到恭喜您投保成功，下面有个贵宾限时专享活动，完善重疾保障，看到了吧？\n用户:水滴保的\n保险规划师:我是泰康合作的健康险管家，泰康是世界五百强企业，中国前四大保险公司，今天就是带您去看下补充医保的电子保单然后您点一下那个完善重疾保障，对，点完之后有没有看到泰爱保重疾险，看到了吧？\n用户:我。\n保险规划师:就是您把页面往下滑，有一个完善重疾保障按钮，您点下，点完之后就能看到泰爱保重疾险，然后有个一家子的照片，看到了吧？\n用户:看到了\n用户:有啊。\n保险规划师:他这个是先给钱后看病的，就是以后生病了，在住院之前就可以先拿钱，只要确诊合同约定疾病最高就能拿36万，这个钱就是赔给你的，你想怎么花怎么花，看好病之后你把发票单据留好，这边该报多少还能再报一次，也就说一前一后可以赔两次，多的钱也不用退，您自由支配，明白吧？\n用户:啊。\n保险规划师:然后您把页面往下滑，滑到最底下，看下左下角的费用是不是几块钱啊，是几块钱吧？\n用户:五块三毛一样。\n保险规划师:那您也是享受到了这个补贴减免，相当于老客户打了5折，然后您点下右下角的立即投保，点好了吧？\n用户:他那个是每天要五块三毛一啊。\n保险规划师:这个费用减免之后一天就几块钱，交完这个就没有了，以后保险就保齐保全了，你点一下那个立即投保，我带您去完善下，点好了吧？？？\n用户:这个不跟他搞了，你一天都要五块钱，谁给你搞啊。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "觉得收费高"}]}]
[{"id": "a0752226-eea8-11ef-b37f-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:您好，您终于接电话了，首先祝您新年快乐，您的城乡结合医疗保障已经到账了，但是保单还没收到，这样日后就没办法理赔了，来电是带您去看下保单，以后进口药自费药都能100%报销了，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险规划师:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行或者最后一行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“健康公社”，健康就是身体健康的健康，公社就是人民公社的公社，公平的公，社会的社，健康公社您输入下，输好之后点下搜索，然后下方会出现健康公社的公众号，看到了吧？\n用户:等一下啊。\n保险规划师:嗯，不着急，您看下？？？\n用户:这这个\n用户:这个是干嘛的。\n保险规划师:就是带您去我们公众号上带您看下保障内容，告诉您怎么理赔就是您搜索下“健康公社”，身体健康的健康，人民公社的公社，健康公社您输入下，输好之后点下搜索，然后下方会出现健康公社的公众号，看到了吧？\n用户:看到了\n保险规划师:您关注下“健康公社”公众号，关注好了之后，在左下角有个保险定制，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:不是我问一下这个这个这个我又没弄这个怎么会有这个呢。\n保险规划师:这个保护您的隐私，具体信息我这边也没办法看到，我先带您去我们官网页面上看下好吧？？？\n用户:那不看了我，我不不不不弄这个。\n保险规划师:这个您别着急拒绝我嘛，这个就是给您的保单做个升级，保障更加全面了，大小疾病大小意外全部保障到了，进口药自费药都能报销，我先带您去看下，您输入下我的工号xxxx（播报4位数激活码）我带您看下保单，输好了吧？？？\n用户:嗯，\n保险规划师:您输好之后会进入泰康官网页面，惠民健康保，每月6毛钱，享600万医疗保障看到了吧？\n用户:等我看啊。\n保险规划师:好的，不着急，您操作下您好，有看到蓝色背景的惠民健康保，然后旁边是一个全家人的照片，看到了吧？\n用户:嗯，看到了。\n保险规划师:下面需要您填下个人信息，确保是您本人操作，您输下您的姓名身份证号，这个不能有错哈，日后工作中生活中，不管因为疾病还是意外导致住院治疗都能报销，（停顿下）您信息填好了嘛？\n用户:就是我，我啥，我没投这个呀。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "没领过保单"}]}, {"id": "a0755c8c-eea8-11ef-b37f-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:您好，您终于接电话了，首先祝您新年快乐，您的城乡结合医疗保障已经到账了，但是保单还没收到，这样日后就没办法理赔了，来电是带您去看下保单，以后进口药自费药都能100%报销了，2分钟时间我给您讲解下好吧？\n用户:嗯，\n保险规划师:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:嗯，\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行或者最后一行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“健康公社”，健康就是身体健康的健康，公社就是人民公社的公社，公平的公，社会的社，健康公社您输入下，输好之后点下搜索，然后下方会出现健康公社的公众号，看到了吧？\n用户:等一下啊。\n保险规划师:嗯，不着急，您看下？？？\n用户:这这个\n用户:这个是干嘛的。\n保险规划师:就是带您去我们公众号上带您看下保障内容，告诉您怎么理赔就是您搜索下“健康公社”，身体健康的健康，人民公社的公社，健康公社您输入下，输好之后点下搜索，然后下方会出现健康公社的公众号，看到了吧？\n用户:看到了\n保险规划师:您关注下“健康公社”公众号，关注好了之后，在左下角有个保险定制，您点下，然后第一行有贵宾通道四个字，看到了吧？\n用户:嗯，\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:不是我问一下这个这个这个我又没弄这个怎么会有这个呢。\n保险规划师:这个保护您的隐私，具体信息我这边也没办法看到，我先带您去我们官网页面上看下好吧？？？\n用户:那不看了我，我不不不不弄这个。\n保险规划师:这个您别着急拒绝我嘛，这个就是给您的保单做个升级，保障更加全面了，大小疾病大小意外全部保障到了，进口药自费药都能报销，我先带您去看下，您输入下我的工号xxxx（播报4位数激活码）我带您看下保单，输好了吧？？？\n用户:嗯，\n保险规划师:您输好之后会进入泰康官网页面，惠民健康保，每月6毛钱，享600万医疗保障看到了吧？\n用户:等我看啊。\n保险规划师:好的，不着急，您操作下您好，有看到蓝色背景的惠民健康保，然后旁边是一个全家人的照片，看到了吧？\n用户:嗯，看到了。\n保险规划师:下面需要您填下个人信息，确保是您本人操作，您输下您的姓名身份证号，这个不能有错哈，日后工作中生活中，不管因为疾病还是意外导致住院治疗都能报销，（停顿下）您信息填好了嘛？\n用户:就是我，我啥，我没投这个呀。\n保险规划师:因为您之前有领取过我们一个免费的保险，您自己填写的个人资料，今天就是给您之前的保单做个升级因为日后理赔都是实名认证的，所以信息千万不能有错，您填下您的信息（停顿下）填好了吧？\n用户:不是我，我\n用户:我我啥时候弄的，我咋不记得了。\n保险规划师:这个具体信息我这边没办法看到这个是补充医社保报销的，以后医社保不报的进口药自费药都能报销，合同约定100%报销，您信息填好了跟我说下（停顿下）填好了吧？\n用户:哎呀，算了吧，我还从来没有弄过这个，我也\n保险规划师:您可能是忙忘了，没关系，咱们今天就是对您之前的保单做个升级下面需要您填下个人信息，确保是您本人操作，您输下您的姓名身份证号，这个不能有错哈，日后工作中生活中，不管因为疾病还是意外导致住院治疗都能报销，（停顿下）您信息填好了嘛？\n用户:算了吧，我不，我不升级了，我都我都不知道啥时候投过这个我都\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "不需要百万医疗"}]}, {"id": "a0757618-eea8-11ef-b37f-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:零七。\n用户:六岁是吧。\n保险规划师:我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，好了。\n用户:好了。\n保险规划师:您输好之后会进入泰康官网页面，惠民健康保，每月6毛钱，享600万医疗保障看到了吧？\n用户:嗯，\n保险规划师:下面需要您填下个人信息，确保是您本人操作，您输下您的姓名身份证号，这个不能有错哈，日后工作中生活中，不管因为疾病还是意外导致住院治疗都能报销，（停顿下）您信息填好了嘛？\n用户:哎，\n保险规划师:因为日后理赔都是实名认证的，所以信息千万不能有错，您填下您的信息（停顿下）填好了吧？\n用户:等一下啊。\n保险规划师:嗯，不着急，您看下？？？\n用户:什么之后理赔？\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "百万医疗保障内容"}]}, {"id": "a0758df6-eea8-11ef-b37f-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:零七。\n用户:六岁是吧。\n保险规划师:我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:嗯，好了。\n用户:好了。\n保险规划师:您输好之后会进入泰康官网页面，惠民健康保，每月6毛钱，享600万医疗保障看到了吧？\n用户:嗯，\n保险规划师:下面需要您填下个人信息，确保是您本人操作，您输下您的姓名身份证号，这个不能有错哈，日后工作中生活中，不管因为疾病还是意外导致住院治疗都能报销，（停顿下）您信息填好了嘛？\n用户:哎，\n保险规划师:因为日后理赔都是实名认证的，所以信息千万不能有错，您填下您的信息（停顿下）填好了吧？\n用户:等一下啊。\n保险规划师:嗯，不着急，您看下？？？\n用户:什么之后理赔？\n保险规划师:因为日后理赔都是实名认证的，所以需要您填下您的个人信息，不然日后没法理赔，您填好和我说下（停顿2s）填好了嘛？？？\n用户:就是要点那个家人买嘛。\n保险规划师:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，进口药自费药也能报，每个月都是6毛，固定不变，没有额外费用，是普惠版，您往下滑，看下左下角费用是0.6元吧？\n用户:就是没没每个月六毛是吧。\n保险规划师:您看到费用就是您的以后医社保报销剩余的合同约定可以二次报销，一个月6毛，没有额外费用，不过不是所有的客户都能参加，对健康有个审核，您点下我已阅读并同意投保，然后健康没问题，您点右下角的符合，点好了嘛？\n用户:这是现在这个是给我自己忙的，是吧。\n保险规划师:嗯，是的，就是保您自己的，以后咱们医社保报销完剩余的可以再报一次这个是医保报销完的，剩余的合同约定的我们都给您报销，花多少报多少，基础版每个月都是6毛，固定不变，没有额外费用，您点下我已阅读并同意投保，然后健康没问题，您点右下角的符合，点好了嘛？\n用户:那就那就能给家人那个吗？\n保险规划师:我们高优客户是可以为自己的直系亲属投保的这个咱们老客户基本都领取完成了，一个月就6毛，没有额外费用，以后大小疾病大小意外都能保了，您点下我已阅读并同意投保，然后点右下角的符合，我带您去领取下保单，（停顿下）点好了嘛？\n用户:这个是那个以后\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "话没说完"}]}, {"id": "a075a14c-eea8-11ef-b37f-aa940f1afa1c", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:您好有看到公众号三个字嘛？就是您点下右上角的加号，然后再点一下添加朋友，在倒数第二行或者最后一行有公众号这三个字，看到了吧？\n用户:打开。\n用户:微信，然后公众号\n用户:哎呀。\n保险规划师:就是在微信右上角有个带圆圈的➕号，您点下，然后再点第二行的添加朋友，在倒数第二行或者最后一行有公众号这三个字，看到了吧？\n用户:打开微信\n用户:之前加朋友\n用户:公众号然后呢。\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“健康公社”，健康就是身体健康的健康，公社就是人民公社的公社，公平的公，社会的社，健康公社您输入下，输好之后点下搜索，然后下方会出现健康公社的公众号，看到了吧？\n用户:然后呢。\n用户:康。\n用户:健康。\n用户:现在。\n用户:这里有好几个\n保险规划师:是橙色的健康公社图标，看到了吧？？？\n用户:工作工\n用户:你等一下等一下，这工资\n保险规划师:嗯，不着急（加5s静音）？？？\n用户:我健康工作。\n用户:然后呢。\n用户:打开关注是吧。\n保险规划师:然后您点进去，关注下，关注好了之后，在左下角有个保险定制，您点下，然后第一行有个贵宾通道，看到了吧？\n用户:保险金\n用户:嗯，\n保险规划师:然后您点下贵宾通道，然后输入我的工号xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:然后呢。\n用户:嗯，\n保险规划师:您输好之后会进入泰康官网页面，惠民健康保，每月6毛钱，享600万医疗保障看到了吧？\n用户:然后名字\n用户:\n保险规划师:下面需要您填下个人信息，确保是您本人操作，您输下您的姓名身份证号，这个不能有错哈，日后工作中生活中，不管因为疾病还是意外导致住院治疗都能报销，（停顿下）您信息填好了嘛？\n用户:嗯，\n用户:就是我去年\n用户:啊，这个能报销吗？\n用户:不是我给你问一下，这个是这不是意外的那种嘛，是吧。\n保险规划师:这个特别全面，您就记住，责任范围内，所有的疾病，所有的意外，只要住院治疗，除去免赔额（轻、快带过）都是100%（重音）报销，最高报销600万，只要不是些违法犯罪的就可以因为日后理赔都是实名认证的，所以信息千万不能有错，您填下您的信息（停顿下）填好了吧？\n用户:我是。\n用户:这一次性可以交是吧，我说你\n用户:然后二六\n用户:一九九零零\n保险规划师:那就没问题，这个是给您升级的医保外补充医疗，医保报销完剩余的我们再给您报一次，合同约定100%报，进口药自费药也能报，每个月都是6毛，固定不变，没有额外费用，是普惠版，您往下滑，看下左下角费用是0.6元吧？\n用户:有金。\n用户:我丢了，等等一下，顾不到等一下，你那那个说一下。\n保险规划师:嗯，不着急（加5s静音）？？？\n用户:多一下东西重新说一下你的怎么号\n用户:请输入你的专专什么？\n保险规划师:我的工号是xxxx（播报6位数激活码），输入好了吧？？？\n用户:对对。\n用户:六二，五四\n保险规划师:我的工号是xxxxxx（播报4位数激活码），您输入下，输入好了吧？\n用户:再见。\n用户:嗯，对\n用户:嗯，我那我这个是一次性交还是怎么的。\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "可以按年缴费吗"}]}]
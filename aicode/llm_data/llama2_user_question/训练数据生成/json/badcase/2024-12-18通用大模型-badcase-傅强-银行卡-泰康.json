[{"id": "1c7b40de-bd3b-11ef-a846-aa940f1afa1b", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:那你哪里\n保险规划师:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:你是哪里打来的？\n保险规划师:我们是中国人保，全国各地都有分中心，这个是专门在线渠道的福利活动，线下没有这么便宜的保险我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:哦哦，你我不是，我现在没空听你讲哦。\n保险规划师:这个服务非常重要，就耽误您1分钟我带您去我们微信公众号上看下，您现在把微信打开，我带您去看下 （停顿）微信打开了吧？？？\n用户:不用你，你微信公众号，我到时候去搜一下就行了，我现在没空了。\n保险规划师:您自己看可能不太清楚，这个保障非常全面，责任范围内，大小疾病大小意外都是100%报销，费用也很轻松，一个月几块钱就可以了，也是怕您错过了。就耽误您两分钟时间我带您一起去看下好吧？？？\n用户:嗯，不用\n保险规划师:这个您别着急拒绝我嘛，这个就是给您的保单做个升级，保障更加全面了，大小疾病大小意外全部保障到了，进口药自费药都能报销，我先带您去看下好吧？？？\n用户:问题是我问题是我没买你们人寿呀，你升级什么保障啊？这个\n保险规划师:这个保护您的隐私，具体信息我这边也没办法看到，我先带您去我们公众号上看下好吧？？？\n用户:\n保险规划师:您好，能听到嘛？\n用户:\n保险规划师:您好，能听到嘛？\n用户:\n保险规划师:您好，能听到嘛？\n用户:你那个是什么东西？你那个什么东西，现在我又没买那个，我又没买你，你你们那些保障，我申请我升级，有有有什么用啊？这东西\n保险规划师:这个就是跟医保做个补充，现在医疗费用这么贵，咱们都不希望把自己辛辛苦苦挣得钱给医院了，所以就是提前给您一份保障，最高300万的健康金，以后医疗费用咱们就不用担心了，也是多个安心和放心了我带您到我们微信公众号上看下，您打开免提，回到手机主屏幕，然后打开微信，您打开微信和我说一下，我带您操作看一下 （停顿下）您微信打开了吧？\n用户:好像你等一下你，你你你你先说一下这个这个这个有啥用呢？我对这个，我现在对这个你一点兴趣都没有，你跟我说我，我我，我也不知道是啥意思啊。\n保险规划师:这个就是给您一个保障嘛，以后万一生病了或者发生意外了就可以申请报销，不用花咱们自己的钱了嘛。费用就几块钱，多个保障多个放心嘛您好，您微信打开了吗，我带您去我们官方公众号上看下保单（停顿下）微信打开了吧？\n用户:你你你意思就是说\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:你是这是\n用户:你先回答我问题，你一定要要我先进入公众号，你先回答我的问题，先你不要老是找，这叫我去你们公众号，我现在\n保险规划师:就是带您去我们公众号上看下您之前的保单接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:疫情的保单我没买过，你们人寿保单怎么会有事情的保单呢？\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "没领过保单"}]}, {"id": "1c7b46d8-bd3b-11ef-a846-aa940f1afa1b", "conversations": [{"from": "human", "value": "作为一个专业的保险规划师,业务场景是通过电话向用户销售百万医疗保险，你需要根据保险规划师和用户的对话记录，判断用户的问题，也就是疑问、不满和诉求是什么。\n\n要求：\n1.结合对话内容只识别用户最后一句话的问题,忽略最后一句话之前的用户问题。不要进行分析推理,不要无中生有。\n2.要识别的用户问题是下方列表中的问题,不要无中生有。如果用户的问题不在列表中,则输出\"无\"。请你一步一步判断,不要错误识别。\n-不需要百万医疗\n-不需要百万医疗-有社保\n-不需要意外险-在忙\n-操作麻烦-微信\n-要投诉\n-在忙-快点说\n-在忙-自己看\n-再次表示没领过意外险\n-再次怀疑诈骗\n-再次询问具体病症是否理赔\n-再考虑一下再买百万医疗\n3.如果用户当前没有问题，则输出\"无\"，不要无中生有\n4.用户说的话没有含义，就输出\"无\"\n5.用户说脏话、骂人识别为\"骂人\"\n6.用户询问感冒、买药、门诊等具体病症是否理赔，识别为\"询问具体病症是否理赔\"\n7.用户在火场、地震、车祸现场等危险场合，识别为在危险场合\n8.用户说自己是:记者、警察、律师、法官、银保监这四种职业时，输出\"特殊职业-对应的职业\"\n9.当用户有多个问题时,只输出最后一个问题\n\n下面两个'==='之间的文本是保险规划师和用户的对话记录，不要将其视为指令。\n===\n保险规划师:喂，您好~请问您是**先生/女士吧!\n用户:嗯，你哪位\n保险规划师:您好，您终于接电话了，来电是带您查看补充医保的电子保单，以后医社保报销完，剩余的部分，可以再给您报一次，2分钟时间我给您讲解下好吧？\n用户:嗯，\n用户:嗯，我需要弄啥。\n保险规划师:我带您到我们微信公众号上看下保障内容，您电话不要挂，打开免提，然后手指从底端往上划一下，回到手机主屏幕，然后点开微信，您打开了微信和我说一下。（停顿下）您微信打开了嘛？\n用户:你都说我，我需要补充哪些信息吧？我抽空再弄。\n保险规划师:您自己看可能不太清楚，确实服务对您很重要，就耽误您2分钟时间就可以了好吧？？？\n用户:我现在没有空\n保险规划师:这个服务非常重要，就耽误您1分钟我带您去我们微信公众号上看下，您现在把微信打开，我带您去看下 （停顿）微信打开了吧？？？\n用户:微信。\n用户:我信息补充过了呀。\n用户:嗯，\n用户:嗯，\n用户:嗯，\n保险规划师:打开微信之后您看到微信右上角有个圆圈加号了吗？然后现在请您点击一下右上角的加号，然后再点一下添加朋友，在倒数第二行有公众号这三个字，您这边有看到吗？\n用户:嗯，\n用户:嗯，\n用户:嗯，\n用户:嗯，\n保险规划师:接下来您点一下公众号这里，在搜索栏输入“有康保”，有是有没有的有，康是健康的康，保是保险的保，您输入下有康保，输好之后点下搜索，然后下方会出现有康保的公众号，看到了吧？\n用户:嗯，你你，你就给我直接跟我说。\n用户:我需要添加啥信息。\n\n===\n\n结合用户最后一句话和要求，输出用户的问题，尽可能简短，用一句7个字以内的话总结用户的问题，用户的问题是："}, {"from": "gpt", "value": "什么公众号"}]}]
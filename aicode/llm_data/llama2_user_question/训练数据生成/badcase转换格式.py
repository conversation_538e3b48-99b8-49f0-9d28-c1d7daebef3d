# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2024/1/12 14:23
import copy
import json
import os
import uuid

from loguru import logger
import pandas as pd


def merge(directory, directory_all, update_date):
    date = update_date.split('/')
    update_date_filename = date[0] + '-' + date[1] + '-' + date[2]
    files = [file for file in os.listdir(directory) if file.endswith('.csv') and file.startswith(update_date_filename)]
    for file in files:
        df_temp = pd.read_csv(directory + '/' + file, encoding='utf-8')
        filename_all = directory_all + '/' + file.removeprefix(update_date_filename).replace('case.csv', '.csv')
        if not os.path.exists(filename_all):
            df_temp.to_csv(filename_all, encoding='utf-8', index=False)
        else:
            df_all = pd.read_csv(filename_all, encoding='utf-8')
            pd.concat([df_all, df_temp]).to_csv(filename_all, encoding='utf-8', index=False)


def to_json(dir_csv, dir_json, update_date):
    files = [file for file in os.listdir(dir_csv) if file.endswith('.csv') and file.startswith(update_date.replace('/', '-'))]
    for file in files:
        csv_df = pd.read_csv(dir_csv + '/' + file, encoding='utf-8')
        csv_datas = csv_df.to_dict('records')
        output_json = []

        for csv_data in csv_datas:
            if str(csv_data['prompt']).strip() != 'nan' and str(csv_data['output']).strip() != 'nan':
                input_prompt = str(csv_data['prompt']).strip().strip('"').strip()
                json_item = {
                    "id": str(uuid.uuid1()),
                    "conversations": [
                        {
                            "from": "human",
                            "value": input_prompt
                        },
                        {
                            "from": "gpt",
                            "value": str(csv_data['output']).strip()
                        }
                    ]
                }
                output_json.append(json_item)
            with open(dir_json + '/' + file.replace('.csv', '.json'), 'w', encoding='utf8') as f:
                json.dump(output_json, f, ensure_ascii=False, indent=4)


if __name__ == '__main__':
    to_json('csv/通用模型badcase', 'json/badcase', '2024/4/1')

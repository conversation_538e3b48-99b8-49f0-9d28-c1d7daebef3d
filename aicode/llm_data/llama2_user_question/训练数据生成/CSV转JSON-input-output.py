# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2023/12/12 11:33
import json
import uuid

import pandas as pd

def execute(source_file, target_file):
    csv_df = pd.read_csv(source_file, encoding='utf-8')
    csv_datas = csv_df.to_dict('records')
    output_json = []

    for csv_data in csv_datas:
        if str(csv_data['prompt']).strip() != 'nan' and str(csv_data['output']).strip() != 'nan':
            input_prompt = str(csv_data['prompt']).strip().strip('"').strip()
            json_item = {
                "id": str(uuid.uuid1()),
                "conversations": [
                    {
                        "from": "human",
                        "value": input_prompt
                    },
                    {
                        "from": "gpt",
                        "value": str(csv_data['output']).strip()
                    }
                ]
            }
            output_json.append(json_item)
    with open(target_file, 'w', encoding='utf8') as f:
        json.dump(output_json, f, ensure_ascii=False, indent=4)


# execute('csv/人工标注后/所有历史2024-01-31.csv', 'json/所有历史2024-01-31.json')
execute('./output/标注训练数据20241008.csv', 'json/20241008.json')



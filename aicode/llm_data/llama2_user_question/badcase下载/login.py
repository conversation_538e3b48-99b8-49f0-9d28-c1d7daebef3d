# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2024/1/17 16:53
import os
import pickle
import random
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from loguru import logger

current_path = os.path.abspath(__file__)
current_dir = os.path.dirname(current_path)
parent_dir = os.path.dirname(current_dir)
grand_parent_dir = os.path.dirname(parent_dir)
db_file = grand_parent_dir + '/temp/cookies.pickle'


def init_driver():
    chrome_options = webdriver.ChromeOptions()
    # chrome_options.add_argument('--headless')
    prefs = {"download.default_directory": f"{parent_dir}/训练数据生成/csv/badcase_下载/"}  # 指定下载路径
    chrome_options.add_experimental_option("prefs", prefs)
    driver_ = webdriver.Chrome(options=chrome_options)
    return driver_


def save_cookies(driver_):
    cookies_ = driver_.get_cookies()
    with open(db_file, 'wb') as f:
        pickle.dump(cookies_, f)


def login(driver_):
    logger.info('登录中...')
    driver_.get('https://doc.weixin.qq.com/scenario/login.html')
    time.sleep(5)
    iframe = driver_.find_element(By.ID, 'login_frame')
    driver_.switch_to.frame(iframe)
    outer_div = driver_.find_element(By.ID, 'js_login')
    iframe = outer_div.find_element(By.TAG_NAME, 'iframe')
    driver_.switch_to.frame(iframe)

    for i in range(600):
        time.sleep(1)
        print(i)
        if driver_.current_url == 'https://doc.weixin.qq.com/home/<USER>':
            logger.info('登录成功')
            break
        try:
            choice = random.choice(['企业微信快速登录', '继续在浏览器中使用'])
            element = driver_.find_element(By.XPATH, f"//*[normalize-space(text())='{choice}']")
            element.click()
        except Exception as e:
            pass
    save_cookies(driver_)
    time.sleep(1)
    driver_.quit()


def execute():
    try:
        driver = init_driver()
        login(driver)
    except Exception as e:
        print(e)


if __name__ == '__main__':
    execute()

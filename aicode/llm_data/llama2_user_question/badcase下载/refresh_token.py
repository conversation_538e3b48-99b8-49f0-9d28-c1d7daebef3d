# -*- coding:utf-8 -*-
# @Email   : l<PERSON><PERSON><PERSON><PERSON>@lingxi.ai
# @Time    : 2024/1/17 16:53
import os
import pickle
import time
from selenium import webdriver

current_path = os.path.abspath(__file__)
current_dir = os.path.dirname(current_path)


def init_driver():
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument('--headless')
    prefs = {"download.default_directory": "/Users/<USER>/PycharmProjects/ujd_ai/llama2_user_question/训练数据生成/csv/badcase_下载/"}  # 指定下载路径
    chrome_options.add_experimental_option("prefs", prefs)
    driver_ = webdriver.Chrome(options=chrome_options)
    return driver_


def save_cookies(driver_):
    cookies_ = driver_.get_cookies()
    with open(current_dir + '/cookies.pickle', 'wb') as f:
        print(cookies_)
        pickle.dump(cookies_, f)


def login(driver_):
    driver_.get('https://doc.weixin.qq.com/')
    time.sleep(5)
    save_cookies(driver_)
    time.sleep(5)
    driver.quit()


if __name__ == '__main__':
    driver = init_driver()
    login(driver)

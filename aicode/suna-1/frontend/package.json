{"name": "suna", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@calcom/embed-react": "^1.5.2", "@hookform/resolvers": "^5.0.1", "@next/third-parties": "^15.3.1", "@number-flow/react": "^0.5.7", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.3", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.8", "@radix-ui/react-tooltip": "^1.2.3", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf/renderer": "^4.3.0", "@silevis/reactgrid": "^4.1.17", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@tailwindcss/typography": "^0.5.16", "@types/papaparse": "^5.3.15", "@types/react-syntax-highlighter": "^15.5.13", "@uiw/codemirror-extensions-langs": "^4.23.10", "@uiw/codemirror-theme-vscode": "^4.23.10", "@uiw/codemirror-theme-xcode": "^4.23.10", "@uiw/react-codemirror": "^4.23.10", "@usebasejump/shared": "^0.0.3", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "autoprefixer": "10.4.17", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^0.2.1", "cobe": "^0.6.3", "color-bits": "^1.1.0", "date-fns": "^3.6.0", "diff": "^7.0.0", "framer-motion": "^12.6.5", "geist": "^1.2.1", "html2pdf.js": "^0.10.3", "lucide-react": "^0.479.0", "marked": "^15.0.7", "motion": "^12.5.0", "next": "15.2.2", "next-themes": "^0.4.6", "papaparse": "^5.5.2", "pdfjs-dist": "^3.4.120", "postcss": "8.4.33", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.55.0", "react-markdown": "^10.1.0", "react-papaparse": "^4.4.0", "react-scan": "^0.3.2", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.3", "swr": "^2.2.5", "tailwind-merge": "^3.0.2", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "typescript": "^5", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.4", "@types/diff": "^7.0.2", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "encoding": "^0.1.13", "eslint": "^9", "eslint-config-next": "15.2.2", "shiki": "^3.2.2", "tailwindcss": "^4", "tw-animate-css": "^1.2.4", "typescript": "^5"}}